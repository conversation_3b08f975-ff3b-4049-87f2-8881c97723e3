<template>
  <div class="contries-page">
    <header>
      <site-header lang="zh" source="countries" @contact-us="status.showForm = true" />
      <div class="header-banner">
        <div class="header-banner-text">
          <h1 class="header-title">确定全球雇主成本</h1>
          <p class="header-desc">
            准备好在另一个国家/地区雇用员工了吗？根据团队成员位置即时汇总雇佣成本，为您的企业做出最佳的招聘举措。
          </p>
        </div>
        <div class="header-form">
          
          <div class="form-body">
            <el-form
              :model="form"
              ref="formRef"
              @change="handleFormChange"
            >
              <el-form-item
                label=""
                prop="country"
                :rules="[
                  { required: true, message: '请选择要查询的国家/地区' },
                ]"
                ><el-select
                  size="large"
                  v-model="form.country"
                  placeholder="国家/地区"
                  @change="onCountryChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in countryList"
                    :key="item.areaCode"
                    :label="item.areaNameI18n"
                    :value="item.areaCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="provinceList.length"
                label=""
                prop="province"
                :rules="[
                  { required: true, message: '请选择要查询的省份/州' },
                ]"
                ><el-select
                  size="large"
                  v-model="form.province"
                  placeholder="省份/州"
                  @change="handleFormChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in provinceList"
                    :key="item.key"
                    :label="item.areaNameI18n"
                    :value="item.key"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="cityList.length"
                label=""
                prop="city"
                :rules="[
                  { required: true, message: '请选择城市' },
                ]"
                ><el-select
                  size="large"
                  v-model="form.city"
                  placeholder="城市"
                  @change="handleFormChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in cityList"
                    :key="item.key"
                    :label="item.areaNameI18n"
                    :value="item.key"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="employTypeList.length"
                label=""
                prop="employType"
                :rules="[{ required: true, message: '请选择员工类型' }]"
                ><el-select
                  size="large"
                  v-model="form.employType"
                  placeholder="员工类型"
                  @change="handleFormChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in employTypeList"
                    :key="item.key"
                    :label="item.sectionNameI18n"
                    :value="item.key"
                  />
                </el-select>
              </el-form-item>
              <el-form-item v-if="form.currency" label="" prop="currency">
                <el-input
                  size="large"
                  v-model="form.currency"
                  disabled
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item
                label=""
                prop="annualSalary"
                :rules="[
                  { required: true, message: '请输入员工的年薪总额' },
                  { type: 'number', message: '请确认您输入的是正确的数字。' },
                ]"
                ><el-input
                  size="large"
                  v-model.number="form.annualSalary"
                  placeholder="员工年度薪资"
                  @input="handleFormChange"
                  style="width: 100%"
                />
              </el-form-item>
              
              <div class="errorMessage" @click="status.showForm = true">
                {{ errorMessage }}
              </div>

              <el-button :disabled="errorMessage !== '' || !form.country || !form.annualSalary || (cityList.length > 0 && !form.city)" class="calculator-submit" @click="fetchEmployCost"
                >获取雇主成本</el-button
              >
            </el-form>
          </div>
        </div>
      </div>
    </header>

    <div id="calculate-result" class="calculator-container">
      <h2 class="result-title">获取世界各地的雇主成本</h2>
      <p class="result-description">
        SmartDeer
        可以帮助您决定雇佣下一位新员工的最佳地点，并全面了解您预计要支付的税费、费用、福利等。
      </p>
      <div class="periodChange" v-if="status.showTable">
        <client-only>
          <el-dropdown @command="handlePeriodChange">
            <div class="el-dropdown-time" @click.prevent>
              <img width="12" src="~/assets/images/calculator/icon-choose-time.png" alt="">
              {{ period === 'year' ? '年' : '月' }}
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="year">年</el-dropdown-item>
                <el-dropdown-item command="month">月</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </client-only>
      </div>
      <table v-if="status.showTable" class="result-table">
        <tr>
          <td class="cost-name">{{  period === 'year' ? '年' : '月'}}薪总额</td>
          <td class="cost-amount">
            {{ form?.currency }}
            {{ period === 'year' ? formatNumber(form?.annualSalary) : formatNumber(calculateRes?.salaryMonthly) }}
          </td>
        </tr>
        <tr>
          <td class="cost-name cost-position">
            <span class="icon">
              <ArrowDown
                v-if="!status.showTableDetails"
                @click="
                  () => {
                    status.showTableDetails = true;
                  }
                "
              />
              <ArrowUp
                v-if="status.showTableDetails"
                @click="
                  () => {
                    status.showTableDetails = false;
                  }
                "
              />
            </span>
            <span>{{  period === 'year' ? '年' : '月'}}度总计雇佣成本</span>
          </td>
          <td class="cost-amount">
            {{ form?.currency }}
            {{ period === 'year' ? formatNumber(calculateRes.netTotalCosts) : formatNumber(calculateRes.netTotalCostsMonthly) }}
          </td>
        </tr>
        <template v-if="status.showTableDetails">
          <tr
            v-for="(cost, cost_index) in period === 'year' ? calculateRes?.costs : calculateRes?.costsMonthly"
            :key="cost_index"
          >
            <td class="cost-name-detail">{{ cost?.sectionNameI18n }}</td>
            <td class="cost-amount-detail">
              {{ form?.currency }} {{ formatNumber(cost?.costOrigin) }}
            </td>
          </tr>
        </template>
        <tr>
          <td class="cost-name">{{  period === 'year' ? '年' : '月'}}度总计成本</td>
          <td class="cost-amount">
            {{ form?.currency }}
            {{ period === 'year' ? formatNumber(calculateRes?.totalCosts) : formatNumber(calculateRes?.totalCostsMonthly) }}
          </td>
        </tr>
      </table>
      <p v-if="status.showTable" class="result-notice">
        计算结果是根据一个国家 /
        地区的当地税收和合规成本得出的估计数字，净付款和雇主缴款可能会根据员工的个人数据而变化。
      </p>
      <p v-if="status.showTable" class="result-notice " v-for="item in descriptionI18n" :key="item">
        {{ item }}
      </p>
      <p class="entity-notice">
        SmartDeer 在全球拥有实体。这意味着您可以在所有这些国家 /
        地区雇佣员工，而无需开设自己的公司实体。
      </p>
      <button class="calculator-contact" @click="handleContactUs">
        联系我们
      </button>
    </div>
    <el-dialog v-model="status.showForm" title="" :width="350">
      <client-only>
        <contact-us-form @submit="submitSuccess" lang="zh" />
      </client-only>
    </el-dialog>
    <site-footer
      lang="zh"
      @contact-us="
        () => {
          status.showForm = true;
        }
      "
    />
  </div>
</template>

<script lang="ts" setup>
import {
  ElNotification,
  ElDialog,
  ElForm,
  ElFormItem,
  ElButton,
  ElInput,
  ElSelect,
  ElOption,
  ElMessage,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElCarousel,
  ElCarouselItem,
} from "element-plus";
import langTool from "~/assets/utils/lang";
import { getQueryString } from "assets/utils";
import { reactive, ref, onMounted } from "vue";
// import costConfig from "~/assets/utils/global-cost-config";
import { ArrowDown, ArrowUp } from "@element-plus/icons-vue";

definePageMeta({ layout: "basic" });

// 获取运行时配置
const config = useRuntimeConfig()
const baseUrl = config.public.mobile_site_host || 'https://m.smartdeer.work'
const imageUrl = `${baseUrl}/images/calculator_banner.png`
const pageUrl = `${baseUrl}/zh/calculator`

useHead({
  htmlAttrs: { lang: "zh-CN" },
  title: "全球雇主成本计算器 - 海外员工薪酬成本测算工具 | SmartDeer",
  meta: [
    { name: 'description', content: '免费使用SmartDeer全球雇主成本计算器，快速测算150+国家的员工雇佣成本，包括薪酬、税务、社保等费用，助力企业海外招聘决策。' },
    { name: 'keywords', content: '雇主成本计算器,海外员工成本,全球薪酬计算,雇佣成本测算,海外用工成本,EOR成本,国际招聘成本,SmartDeer' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { name: 'author', content: 'SmartDeer' },

    // Open Graph标签
    { property: 'og:title', content: '全球雇主成本计算器 - 海外员工薪酬成本测算工具 | SmartDeer' },
    { property: 'og:description', content: '免费使用SmartDeer全球雇主成本计算器，快速测算150+国家的员工雇佣成本，包括薪酬、税务、社保等费用，助力企业海外招聘决策。' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: pageUrl },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:image', content: imageUrl },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:image:type', content: 'image/png' },
    { property: 'og:image:alt', content: 'SmartDeer全球雇主成本计算器' },
    { property: 'og:locale', content: 'zh_CN' },

    // Twitter Card标签
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: '全球雇主成本计算器 - 海外员工薪酬成本测算工具 | SmartDeer' },
    { name: 'twitter:description', content: '免费使用SmartDeer全球雇主成本计算器，快速测算150+国家的员工雇佣成本，包括薪酬、税务、社保等费用，助力企业海外招聘决策。' },
    { name: 'twitter:image', content: imageUrl },
    { name: 'twitter:image:alt', content: 'SmartDeer全球雇主成本计算器' }
  ],
  link: [
    // Canonical URL
    { rel: 'canonical', href: pageUrl },

    // Hreflang标签
    { rel: 'alternate', hreflang: 'zh', href: `${baseUrl}/zh/calculator` },
    { rel: 'alternate', hreflang: 'en', href: `${baseUrl}/en/calculator` },
    { rel: 'alternate', hreflang: 'ja', href: `${baseUrl}/ja/calculator` },
    { rel: 'alternate', hreflang: 'x-default', href: `${baseUrl}/calculator` }
  ],
  script: [
    // 结构化数据 - WebApplication
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "SmartDeer全球雇主成本计算器",
        "description": "免费的全球雇主成本计算工具，帮助企业快速测算150+国家的员工雇佣成本",
        "url": pageUrl,
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD"
        },
        "provider": {
          "@type": "Organization",
          "name": "SmartDeer",
          "url": baseUrl
        },
        "featureList": [
          "150+国家雇佣成本计算",
          "实时汇率转换",
          "税务和社保费用估算",
          "多币种支持",
          "免费使用"
        ],
        "screenshot": {
          "@type": "ImageObject",
          "url": imageUrl
        }
      })
    }
  ]
});

const scrollItems = [];

const status = reactive({
  showForm: false,
  showConsultantCode: false,
  showTable: false,
  showTableDetails: true,
});

const formRef = ref();

const countryList = ref([]);
const provinceList = ref([]);
const allCityList = ref([]);
const cityList = ref([]);
const employTypeList = ref([]);
const descriptionI18n = ref([])
const errorMessage = ref('')

const form = reactive({
  country: "",
  province: "",
  city: "",
  currency: "",
  employType: "",
  annualSalary: null,
});


const calculateRes = ref({
 
});

function handleFormChange() {
  cityList.value = allCityList.value.filter(item => item.province === form.province);
  // fetchEmployCost();
}

// function handleCountryChange() {
//   form.employType = "";
//   form.province = "";
//   form.currency = supportCurrencies[form?.country][0].value;
// }

function handleContactUs() {
  status.showForm = true;
  // console.log(status);
}

function formatNumber(number) {
  const formatter = new Intl.NumberFormat("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  return formatter.format(number);
}

const period = ref('year');

function handlePeriodChange(cmd) {
  period.value = cmd;
}

function scrollTo(tag) {
  const ele = window.document.querySelector(tag);
  if (ele)
    window.scrollTo({
      top: ele.offsetTop,
      behavior: "smooth",
    });
}

// 自定义指令
const vScrollShow = {
  mounted: (el, bindings) => {
    const delayOffset = bindings.value.delayOffset || 0;
    scrollItems.push({ offsetTop: el.offsetTop + delayOffset, el: el });
  },
};

let timer = null;
function scroll(e) {
  if (timer) return;
  timer = setTimeout(() => {
    const offset = window.scrollY + window.innerHeight;
    scrollItems.forEach((item, index) => {
      if (item.offsetTop < offset) {
        item.el.setAttribute("show", true);
        scrollItems.splice(index, 1);
      }
    });
    timer = null;
  }, 30);
}

const host = "https://www-api.smartdeer.work/v1/website/function/runtime";
const hostTest = "https://www-api-test.smartdeer.work/v1/website/function/runtime";
const fetchCountry = async () => {
  // console.log("API_HOST:", host);
  const res = await fetch(host, {
    method: "POST",
    headers: {
      "Content-Type": "application/json", // 设置请求头为 application/json
      'accept-language': 'zh-CN'
      // 'accept-language': 'en-US'
    },
    body: JSON.stringify({
      functionKey: "search_website_smd_employer_cost_country_conf",
      params: {
        current: 1,
        size: 100,
        limit: 100,
        searchInfo: {
          searchItems: [],
          orders: [
            {
              key: "displayOrder",
              asc: "ASC",
            },
          ],
        },
      },
    }),
  });
  const {
    data: { dataInfo },
  } = await res.json();
  // console.log(dataInfo);
  // debugger;
  countryList.value = dataInfo || [];
};

const fetchCurrency = async (countryCode) => {
  errorMessage.value = '';
  // const res = await fetch(`https://www-api-test.smartdeer.work/v1/website/function/runtime`, {
  const res = await fetch(`${host}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json", // 设置请求头为 application/json
      'accept-language': 'zh-CN'
    },
    body: JSON.stringify({
      functionKey: "x_website_employer_cost_country_conf",
      params: {
        countryCode,
      },
    }),
  });
  const { data } = await res.json();
  if (data?.enableConf?.calculateSupport === 'false') {
    errorMessage.value = data?.enableConf?.descriptionI18n || 'The current country does not support cost calculation.'
    return;
  }
  // console.log(data);
  // debugger;
  provinceList.value = data?.provinceConf || [];
  allCityList.value = data?.cityConf || [];
  employTypeList.value = data?.employTypeConf || [];
  form.currency = data?.currencyConf || "";
  descriptionI18n.value = data?.descriptionConf?.descriptionI18n?.split('\n') || [];
};

// "countryCode": "HK",
// "provinceCode": 'BJ'
// "employeeSalary": "300000"
const fetchEmployCost = async () => {
  if (!form.country || !form.annualSalary) return;
  const params: any = {
    countryCode: form.country,
    employeeSalary: form.annualSalary,
    ...(provinceList.value.length && form.province
      ? { provinceCode: form.province }
      : {}),
    ...(employTypeList.value.length && form.employType
      ? { employType: form.employType }
      : {}),
    ...(cityList.value.length && form.city
      ? { cityCode: form.city }
      : {}),
  };
  const res = await fetch(host, {
    method: "POST",
    headers: { 
      "Content-Type": "application/json",
      'accept-language': 'zh-CN'
    },
    body: JSON.stringify({
      functionKey: "x_website_cal_employer_cost",
      params,
    }),
  });
  const { data } = await res.json();
  calculateRes.value = {};
  calculateRes.value = data;
  scrollTo("#calculate-result");
  status.showTable = true;
};

const onCountryChange = async (val) => {
  form.province = "";
  form.employType = "";
  form.currency = "";
  provinceList.value = [];
  employTypeList.value = [];
  await fetchCurrency(val);
  handleFormChange();
};

onMounted(() => {
  window.addEventListener("scroll", scroll);

  const curScroll = getQueryString("scroll");
  if (curScroll) {
    scrollTo("#" + curScroll);
  }

  fetchCountry();
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", scroll);
});

function switchLang(lang) {
  langTool.swithLang(lang);
}

function submitSuccess() {
  status.showForm = false;
  ElMessage.success("您的请求已收到，我们会尽快与您联系。");
}
</script>

<style lang="scss" scoped>
.errorMessage {
  color: red;
  margin: 20px 0;
  font-size: 14px;
}
header {
  background: #fff6ec;
  .header-banner {
    position: relative;
    width: 1204px;
    height: 610px;
    margin: 0 auto;
    // background-image: url("~/assets/images/calculator/banner.png");
    background-size: 1204px 590px;
    background-position: 0px;
    background-repeat: no-repeat;
  }

  .header-banner-text {
    position: absolute;
    left: 0;
    width: 641px;
    .header-title {
      font-size: 46px;
      font-weight: bold;
      margin-top: 170px;
    }
    .header-desc {
      font-size: 18px;
      line-height: 30px;
      letter-spacing: 3px;
      font-weight: 300;
    }
  }
  .header-form {
    position: absolute;
    top: 80px;
    right: 0;
    width: 405px;
    background: #fff;
    border: 1px #eff0f6 solid;
    border-radius: 22px;
    box-sizing: border-box;
    box-shadow: 0 10px 14px 0 rgba(74, 58, 255, 0.01),
      0 9px 26px 0 rgba(23, 15, 73, 0.05);
    padding: 40px;

    .calculator-title {
      text-align: center;
    }

    .calculator-submit {
      color: #fff;
      width: 100%;
      height: 40px;
      line-height: 40px;
      border-radius: 20px;
      background: linear-gradient(259deg, #f54a25 -42%, #ffab71 98%);
    }
  }
}
.el-dropdown-time{
  margin-top: 20px;
  display: flex;
  gap: 10px;
  .el-icon--right{
    width: 10px;
    height: 10px;
  }
}
.periodChange {
  display: flex;
  justify-content: flex-end;
  cursor: pointer;
}
.calculator-container {
  width: 1204px;
  margin: 0 auto;

  .result-title {
    margin-top: 50px;
    font-size: 36px;
    text-align: center;
  }

  .result-description {
    font-size: 18px;
    text-align: center;
    color: #6f6c90;
    line-height: 30px;
  }

  .result-table {
    border-collapse: collapse;
    width: 100%;
    margin-top: 20px;
  }

  .result-table tr,
  .result-table th,
  .result-table td {
    border: 1px #dbdee5 solid;
  }

  .result-table td {
    width: 50%;
    font-size: 14px;
    height: 28px;
    padding: 24px 30px;
    color: #170f49;
    font-weight: 500;
  }

  .cost-name-detail,
  .cost-amount-detail {
    background: rgba(239, 239, 239, 0.3);
  }

  .cost-position {
    position: relative;
  }

  .cost-position .icon {
    cursor: pointer;
    position: absolute;
    display: block;
    width: 20px;
    height: 20px;
    right: 18px;
    top: 20px;
  }

  .result-notice {
    color: #6f6c90;
    font-size: 12px;
    line-height: 16px;
    margin-top: 15px;
  }

  .entity-notice {
    margin-top: 68px;
    font-size: 32px;
    font-weight: 400;
    color: #170f49;
  }

  .calculator-contact {
    display: block;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    border-radius: 20px;
    width: 290px;
    height: 40px;
    color: #fff;
    border: 0;
    background: linear-gradient(252deg, #f54a25 -45%, #ffab71 98%);
    box-shadow: 0 4px 8px 0 rgba(255, 129, 42, 0.29);
    margin: 20px auto 75px;
  }
}

@media (max-width: 768px) {
  header {
    .header-banner {
      width: 90vw;
      min-width: 0;
      height: auto;
      background-size: cover;
      background-position: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0 10px;
      position: static;
    }
    .header-banner-text {
      position: static;
      width: 100%;
      margin-top: 40px;
      .header-title {
        font-size: 24px;
        margin-top: 0;
        text-align: center;
      }
      .header-desc {
        font-size: 14px;
        line-height: 22px;
        text-align: center;
      }
    }
    .header-form {
      position: static;
      width: 100%;
      margin: 24px auto;
      padding: 24px 10px;
      box-shadow: none;
      border-radius: 16px;
    }
  }
  .calculator-container {
    width: 90vw;
    min-width: 0;
    padding: 0 10px;
    box-sizing: border-box;
    .result-title {
      font-size: 22px;
    }
    .result-description {
      font-size: 14px;
    }
    .result-table td {
      font-size: 12px;
      padding: 12px 8px;
    }
    .entity-notice {
      font-size: 18px;
    }
    .calculator-contact {
      width: 100%;
      height: 40px;
      font-size: 14px;
      margin: 20px auto 40px;
    }
  }
}
</style>