<template lang="pug">
.contact-us-form
  template(v-if="lang=='zh'")
    .form-title 联系我们
    .form-body
      el-form(:model="form" ref="ruleFormRef")
        el-form-item(:rules="[{required: true, message:'我们如何称呼您？'}]", prop="name")
          el-input(placeholder="姓名*" size="large" v-model="form.name")

        el-form-item(:rules="[{required: true, message:'请输入您的公司'}]", prop="company")
          el-input(placeholder="公司*" size="large" v-model="form.company")

        el-form-item(:rules="[{required: true, message:'请选择您要咨询的服务'}]", prop="service")
          el-select(size="large" placeholder="所需服务*" v-model="form.service" style="width:100%;")
            el-option(v-for="(item, index) in serviceOptions" :key="index" :label="item.zh" :value="item.zh")

        el-form-item(:rules="[{required:true, message:'我们如何通过邮箱联系您？'}, {type:'email', message:'请确认您输入的是正确的邮箱。'}]", prop="email")
          el-input(placeholder="联系邮箱*" size="large" v-model="form.email")

        el-form-item(:rules="mobileRules", prop="mobile")
          el-input(placeholder="联系电话*" size="large" v-model="form.mobile")
            template(#prepend)
              el-select(v-model="form.countryCode" size="large" style="width: 96px;")
                el-option(v-for="(item, index) in phoneArea" :key="index" :label="item.number" :value="item.number")
                  span {{`${item.number} ${item.areaNameCN}`}}

        el-form-item(:rules="[{required: true, message:'请填写您的联系方式'}]", prop="extra")
          el-input(type="textarea" v-model="form.extra" placeholder="需要我们为您提供什么服务？*" rows="6")

        el-form-item()
          el-button(type="primary" style="width: 100%" size="large" @click="submitForm") 提交

  template(v-else-if="lang=='en'")
    .form-title Contact Us
    .form-body
      el-form(:model="form" ref="ruleFormRef")
        el-form-item(:rules="[{required: true}]", prop="name")
          el-input(placeholder="First Name*" size="large" v-model="form.name")

        el-form-item(:rules="[{required: true}]", prop="company")
          el-input(placeholder="Company Name*" size="large" v-model="form.company")

        el-form-item(:rules="[{required: true}]", prop="service")
          el-select(size="large" placeholder="Required Services*" v-model="form.service" style="width:100%;")
            el-option(v-for="(item, index) in serviceOptions" :key="index" :label="item.en" :value="item.zh")

        el-form-item(:rules="[{required:true}, {type:'email'}]", prop="email")
          el-input(placeholder="Email address*" size="large" v-model="form.email")

        el-form-item(:rules="[{required: true, message: 'Please enter your phone number'}]", prop="mobile")
          el-input(placeholder="Phone Number*" size="large" v-model="form.mobile")
            template(#prepend)
              el-select(v-model="form.countryCode" size="large" style="width: 96px;")
                el-option(v-for="(item, index) in phoneArea" :key="index" :label="item.number" :value="item.number")
                  span {{`${item.number} ${item.areaNameEN}`}}

        el-form-item(:rules="[{required: true, message: 'Please describe your requirements'}]", prop="extra")
          el-input(type="textarea" v-model="form.extra" placeholder="Please describe your requirements and we will contact you later*" rows="6")

        el-form-item()
          el-button(type="primary" style="width: 100%" size="large" @click="submitForm") Send

  template(v-else-if="lang=='ja'")
    .form-title お問い合わせ
    .form-body
      el-form(:model="form" ref="ruleFormRef")
        el-form-item(:rules="[{required: true}]", prop="name")
          el-input(placeholder="お名前*" size="large" v-model="form.name")

        el-form-item(:rules="[{required: true}]", prop="company")
          el-input(placeholder="会社名*" size="large" v-model="form.company")

        el-form-item(:rules="[{required: true}]", prop="service")
          el-select(size="large" placeholder="必要なサービス*" v-model="form.service" style="width:100%;")
            el-option(v-for="(item, index) in serviceOptions" :key="index" :label="item.ja" :value="item.ja")

        el-form-item(:rules="[{required: true}]", prop="email")
          el-input(placeholder="メールアドレス*" size="large" v-model="form.email")

        el-form-item(:rules="[{required: true, message: '電話番号を入力してください'}]", prop="mobile")
          el-input(placeholder="電話番号*" size="large" v-model="form.mobile")
            template(#prepend)
              el-select(v-model="form.countryCode" size="large" style="width: 96px;")
                el-option(v-for="(item, index) in phoneArea" :key="index" :label="item.number" :value="item.number")
                  span {{`${item.number} ${item.areaNameJP}`}}

        el-form-item(:rules="[{required: true, message: 'ご要望をご記入ください'}]", prop="extra")
          el-input(type="textarea" v-model="form.extra" placeholder="ご要望をご記入ください。*" rows="6")

        el-form-item()
          el-button(type="primary" style="width: 100%" size="large" @click="submitForm") 送信
</template>

<script lang="ts" setup>
import { ElDialog, ElForm, ElFormItem, ElButton, ElInput, ElSelect, ElOption, ElMessage, ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import phoneArea from '~/assets/utils/global-phone-area'
import {ref} from "vue";
import {getQueryString} from "assets/utils";
import {useSessionStorage} from "@vueuse/core";
const { lang } = defineProps(['lang'])
const emit = defineEmits(['submit'])

const status = reactive({
  loading: false
})

const formTitle = ref<string>('')

const form = reactive({
  name: '',
  company: '',
  service: '',
  countryCode: '+86',
  mobile: '',
  email: '',
  extra: ''
})

const serviceOptions = ref([
  { zh: '全球人才招聘（Recruitment）', en: 'Recruitment', ja: 'グローバル人材採用（Recruitment）' },
  { zh: '全球名义雇主（EOR）', en: 'EOR', ja: 'グローバル名義雇用主（EOR）' },
  { zh: '全球灵活员工（Contractor）', en: 'Contractor', ja: 'グローバルフレックス従業員（Contractor）' },
  { zh: '全球人力资源服务（HRO）', en: 'HRO', ja: 'グローバル人事サービス（HRO）' },
  { zh: '其他', en: 'Other', ja: 'その他' }
])

const validateChineseMobile = (rule: any, value: string, callback: any) => {
  if (form.countryCode === '+86' && value) {
    if (!/^1[3-9]\d{9}$/.test(value)) {
      if (lang === 'ja') {
        callback(new Error('中国大陸の携帯電話番号を入力してください'))
      } else if (lang === 'en') {
        callback(new Error('Please enter a valid Chinese mainland mobile phone number'))
      } else {
        callback(new Error('请输入有效的中国大陆手机号'))
      } 
    } else {
      callback()
    }
  } else {
    callback()
  }
}

const mobileRules = [
  { required: true, message: lang === 'ja' ? '電話番号を入力してください' : lang === 'en' ? 'Please enter a valid Chinese mainland mobile phone number' : '请输入联系电话' },
  { validator: validateChineseMobile, trigger: 'blur' }
]

 async function submitForm() {
  status.loading = true

  const res = await submitFormSage()
  const resFeishu = await submitFormFeishu()

  emit('submit')
  status.loading = false
}

async function submitFormSage () {
  const host = "https://www-api.smartdeer.work/v1/website/function/runtime";
  const host_test = "https://www-api-test.smartdeer.work/v1/website/function/runtime";

  const res = await fetch(host, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'accept-language': 'en-US'
    },
    body: JSON.stringify({
      functionKey: "x_website_add_smd_website_leads",
      params: {
        "contactName": form.name,
        "companyName": form.company,
        "contactMobile": `${form.countryCode} ${form.mobile}`,
        "contactEmail": form.email,
        "serviceNeeded": form.service,
        "serviceContent": form.extra
      },
    }),
  })

  return res
}

async function submitFormFeishu() {
  const USER_REF_KEY = 'USER_REF'
  const userRef = useSessionStorage(USER_REF_KEY, '')
  let parsedRefRst = {
    domain: '',
    keyword: ''
  };
  if (userRef.value) {
    parsedRefRst = parseReferrer(userRef.value);
  }
  
  const host = "https://open.feishu.cn/open-apis/bot/v2/hook/ab070be6-b904-4f95-8d2d-b03496f8202a"
  const host_test = "https://open.feishu.cn/open-apis/bot/v2/hook/b47dd412-27fd-434b-8b79-6efe17951df8"

  const res = await fetch(host, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'accept-language': 'en-US'
    },
    body: JSON.stringify({
      msg_type: "post",
      content: {
        post: {
          zh_cn: {
            title: "Biu~, 您有新的网站咨询请求！",
            content: [
              [
                { tag: "text", text: "姓名: " },
                { tag: "text", text: form.name || '-' }
              ], [
                { tag: "text", text: "公司: " },
                { tag: "text", text: form.company || '-' }
              ], [
                { tag: "text", text: "所需服务: " },
                { tag: "text", text: form.service }
              ], [
                { tag: "text", text: "邮箱: " },
                { tag: "text", text: form.email || '-' }
              ], [
                { tag: "text", text: "手机: " },
                { tag: "text", text: `${form.countryCode} ${form.mobile }` }
              ], [
                { tag: "text", text: "详细信息: " },
                { tag: "text", text: form.extra || '-' }
              ], [
                { tag: "text", text: "来源网站: " },
                { tag: "text", text: parsedRefRst.domain }
              ], [
                { tag: "text", text: "关键词: " },
                { tag: "text", text: parsedRefRst.keyword }
              ]
            ]
          }
        }
      }
    })
  })

  return res
}

function parseReferrer(urlencodedReferrer) {
  // 解码URL
  let referrer = decodeURIComponent(urlencodedReferrer);

  try {
    let url = new URL(referrer);
    let hostname = url.hostname;
    if (hostname.includes("smartdeer.work")) {
      hostname = 'direct'
    }
    let result = {
      domain: hostname,
      keyword: ''
    };

    // 检查是否是百度或谷歌搜索引擎
    if (hostname.includes("baidu.com") || hostname.includes("google.")) {
      let params = new URLSearchParams(url.search);

      if (hostname.includes("baidu.com")) {
        // 百度搜索关键字参数为 "wd"
        result.keyword = params.get("wd");
      } else if (hostname.includes("google.")) {
        // 谷歌搜索关键字参数为 "q"
        result.keyword = params.get("q");
      }
    }

    return result;
  } catch (e) {
    return {
      domain: '',
      keyword: ''
    }
  }
}

onMounted(() => {
  const linkFrom = getQueryString('from')
  if (linkFrom === 'wx_mp') {
    formTitle.value = '公众号'
  } else if (linkFrom === 'mp') {
    formTitle.value = '小程序'
  } else {
    formTitle.value = '网站'
  }
})
</script>

<style lang="scss" scoped>

.contact-us-form {
  .form-title {
    font-size: 24px;
    color: #484848;
    text-align: center;
    margin-bottom: 30px;
    margin-top: -30px;
    font-weight: bold;
  }

  .form-body {
    .mobile {
      line-height: 46px;
      border: 1px solid;
      border-radius: 8px;
      width: 100%;
    }
  }
}
</style>