<template lang="pug">
.contries-page
  site-header(lang="${LANGUAGE_SIMPLE}" @contact-us="status.showForm = true")

  .countries
    .countries-content
      h1.article-title ${TITLE}
      div(v-html="htmlContent" )
  site-footer(lang="${LANGUAGE_SIMPLE}" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '${TITLE}',
  ogDescription: '${DESCRIPTION}',
  ogSiteName: '${SITE_NAME}',
  description: '${DESCRIPTION}'
})
useHead({
  htmlAttrs: {
    lang: '${LANGUAGE}'
  },
  title: '${TITLE}'
})
const status = reactive({
  showForm: false
})
const langSimple = '${LANGUAGE_SIMPLE}';
const pageTitle = '${TITLE}';
const bannerImage = '${FEATURE_IMAGE_URL}';
const flagImage = '${COUNTRY_FLAT_IMAGE_URL}';
const htmlContent = '${CONTENT}';
function submitSuccess() {
  ElMessage.success('${FORM_CONFIRM_PROMPT}')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>