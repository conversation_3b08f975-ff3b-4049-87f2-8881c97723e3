// SmartDeer 动态 Sitemap 生成器
// 针对百度和必应优化的 XML Sitemap

export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig()
  // PC项目使用PC域名
  const baseUrl = config.public.pc_site_host || 'https://www.smartdeer.work'
  
  // 定义支持的语言
  const languages = ['zh', 'en', 'ja']
  
  // 定义主要页面
  const mainPages = [
    '',
    'aboutus',
    'calculator',
    'countries',
    'marketing'
  ]
  
  // 定义文章页面（这里可以从数据库或API获取）
  const articlePages = [
    'articles/global-employment-guide',
    'articles/eor-vs-peo',
    'articles/global-payroll-management',
    'articles/remote-work-compliance'
  ]
  
  // 生成 URL 条目
  const urls: Array<{
    loc: string
    lastmod: string
    changefreq: string
    priority: string
    alternates?: Array<{ hreflang: string; href: string }>
  }> = []
  
  const currentDate = new Date().toISOString().split('T')[0]
  
  // 添加首页
  urls.push({
    loc: baseUrl,
    lastmod: currentDate,
    changefreq: 'daily',
    priority: '1.0',
    alternates: languages.map(lang => ({
      hreflang: lang,
      href: `${baseUrl}/${lang}`
    }))
  })
  
  // 添加语言特定的主页
  languages.forEach(lang => {
    urls.push({
      loc: `${baseUrl}/${lang}`,
      lastmod: currentDate,
      changefreq: 'daily',
      priority: '0.9'
    })
  })
  
  // 添加主要页面
  languages.forEach(lang => {
    mainPages.forEach(page => {
      if (page) {
        urls.push({
          loc: `${baseUrl}/${lang}/${page}`,
          lastmod: currentDate,
          changefreq: 'weekly',
          priority: '0.8'
        })
      }
    })
  })
  
  // 添加文章页面
  languages.forEach(lang => {
    articlePages.forEach(article => {
      urls.push({
        loc: `${baseUrl}/${lang}/${article}`,
        lastmod: currentDate,
        changefreq: 'monthly',
        priority: '0.7'
      })
    })
  })
  
  // 生成 XML
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
${urls.map(url => `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
${url.alternates ? url.alternates.map(alt => 
    `    <xhtml:link rel="alternate" hreflang="${alt.hreflang}" href="${alt.href}" />`
).join('\n') : ''}
  </url>`).join('\n')}
</urlset>`
  
  // 设置响应头
  setHeader(event, 'Content-Type', 'application/xml')
  setHeader(event, 'Cache-Control', 'max-age=3600') // 缓存1小时
  
  return sitemap
})
