<!--
 * @Author: sx <EMAIL>
 * @Date: 2023-01-10 09:55:23
 * @LastEditors: sx <EMAIL>
 * @LastEditTime: 2023-01-13 17:04:51
 * @FilePath: \bpo-website-pc\pages\zh\contries\canada.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template lang="pug">
.contries-page
  header
    site-header(lang="zh" source="countries" @contact-us="status.showForm = true")

    .header-banner
      .header-banner-text
        h1.header-title 在 100 多个国家/地区轻松聘用人才松聘用人才
        p.header-desc 使用下面的国家/地区指南来获取有关雇佣合同、试用期、终止规定、税收和社会保障、带薪休假、签证和公共假期的详细信息。 现在准备好开始了吗？ 联系我们以了解更多信息。
        button.header-contact(@click="status.showForm = true") 联系我们
      .header-banner-image
        img(src="~/assets/images/countries/country-guide-header.png")
  .country-index-main
    h2 探索如何通过SmartDeer进行招聘
    p.sub-desc 在 SmartDeer，我们在全球拥有 25 个 SmartDeer 旗下实体，可实现无缝的员工招聘。 在我们尚未拥有实体的国家/地区，我们与拥有多年经验的顶级合作伙伴合作，确保您可以在任何需要的地方进行招聘。 您可以在下面找到我们在哪些国家/地区拥有合作伙伴、实体和支持（按受欢迎程度排序）。
    .search-bar
      input.text-search(placeholder="搜索国家" v-model="form.keyword" @keyup.enter="searchKeyword")
      button.btn-search(@click="searchKeyword") 搜索
    ul.country-list
      a(v-for="(item, index) in paginator.currentPageData" :href="item.link")
        li
          .img-container
            img(:src="item.image.node.sourceUrl" width="100%")
          .link-container 在{{ item.countryName }}雇佣
    .paginator
      a.prev(v-if="paginator.currentPage !== 1" @click="previousPage" src="javascript:;") 上一页
      a.next(v-if="paginator.currentPage !== paginator.totalPages" @click="nextPage" src="javascript:;") 下一页

  site-footer(lang="zh" @contact-us="status.showForm = true")
  .contact-form
    client-only
      el-dialog(v-model="status.showForm" title="" :width="354")
        contact-us-form(lang="zh" @submit="submitSuccess")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
import articleList from "./article-list-zh.json";
import {ceil} from "lodash-es";

definePageMeta({ layout: 'basic' })
useSeoMeta({
  ogTitle: '在全球 150 多个国家轻松招聘 - SmartDeer',
  ogDescription: '使用 SmartDeer 的国家/地区指南来获取有关雇佣合同、试用期、终止规定、税收和社会保障、带薪休假、签证和公共假期的详细信息。 现在准备好开始了吗？ 联系我们以了解更多信息。',
  ogSiteName: 'SmartDeer',
  description: '使用 SmartDeer 的国家/地区指南来获取有关雇佣合同、试用期、终止规定、税收和社会保障、带薪休假、签证和公共假期的详细信息。 现在准备好开始了吗？ 联系我们以了解更多信息。'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在全球 150 多个国家轻松招聘 - SmartDeer'
})

const status = reactive({
  showForm: false
})

const form = reactive({
  keyword: ''
})

const paginator = reactive({
  currentPage: 1,
  pageSize: 12,
  totalPages: 0,
  allArticles: articleList,
  filteredArticles: [],
  currentPageData: []
})

function nextPage() {
  if (paginator.currentPage < paginator.totalPages) {
    paginator.currentPage++;
  }
  freshCurrentPageData();
}

function previousPage() {
  if (paginator.currentPage > 1) {
    paginator.currentPage--;
  }
  freshCurrentPageData();
}

function searchKeyword() {
  if (!form.keyword) {
    paginator.filteredArticles = paginator.allArticles;
  } else {
    const searchKeywordLower = form.keyword.toLowerCase();
    paginator.filteredArticles = paginator.allArticles.filter(item => {
      return JSON.stringify(item).toLowerCase().includes(searchKeywordLower);
    });
  }
  paginator.currentPage = 1
  freshCurrentPageData()
}

function freshCurrentPageData() {
  if (!form.keyword) {
    paginator.filteredArticles = paginator.allArticles
  }
  const start = (paginator.currentPage - 1) * paginator.pageSize
  const end = paginator.currentPage * paginator.pageSize
  paginator.currentPageData = paginator.filteredArticles.slice(start, end)
  paginator.totalPages = ceil(paginator.filteredArticles.length / paginator.pageSize)
}

function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
  status.showForm = false
}

function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}

onMounted(() => {
  freshCurrentPageData()
})
</script>

<style lang="scss" scoped>
@import './country-guide-index.scss';
</style>