{"private": true, "scripts": {"build": "nuxt build", "dev": "set BUILD_ENV=development && nuxt dev", "generate:production": "export BUILD_ENV=production &&  nuxt generate", "generate:test": "export BUILD_ENV=test &&  nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"nuxt": "3.0.0-rc.12", "video.js": "^8.21.0"}, "dependencies": {"@coze/api": "^1.0.15", "@element-plus/icons-vue": "^2.0.10", "axios": "^1.7.9", "element-plus": "^2.2.20", "graphql-request": "^6.1.0", "pug": "^3.0.2", "sass": "^1.55.0", "unplugin-element-plus": "^0.4.1", "weixin-js-sdk": "^1.6.5"}}