<template>
  <div class="contries-page">
    <header>
      <site-header lang="ja" source="countries" :showContactUs="false" />
      <div class="header-banner">
        <div class="header-banner-text">
          <h1 class="header-title">世界各国の従業員コストを即時にシミュレーション</h1>
          <p class="header-desc">
            海外での採用をお考えですか？チームメンバーの所在地に基づいて、雇用コストを即座に試算し、最適な採用判断を支援します。
          </p>
        </div>
        <div class="header-form">

          <div class="form-body">
            <el-form
              :model="form"
              ref="formRef"
              @change="handleFormChange"
            >
              <el-form-item
                label=""
                prop="country"
                :rules="[
                  { required: true, message: '国・地域を選択してください' },
                ]"
                ><el-select
                  size="large"
                  v-model="form.country"
                  placeholder="国・地域"
                  @change="onCountryChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in countryList"
                    :key="item.areaCode"
                    :label="item.areaNameI18n"
                    :value="item.areaCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="provinceList.length"
                label=""
                prop="province"
                :rules="[
                  { required: true, message: '都道府県を選択してください' },
                ]"
                ><el-select
                  size="large"
                  v-model="form.province"
                  placeholder="都道府県"
                  @change="handleFormChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in provinceList"
                    :key="item.key"
                    :label="item.areaNameI18n"
                    :value="item.key"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="cityList.length"
                label=""
                prop="city"
                :rules="[
                  { required: true, message: '市区町村を選択してください' },
                ]"
                ><el-select
                  size="large"
                  v-model="form.city"
                  placeholder="市区町村"
                  @change="handleFormChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in cityList"
                    :key="item.key"
                    :label="item.areaNameI18n"
                    :value="item.key"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="employTypeList.length"
                label=""
                prop="employType"
                :rules="[{ required: true, message: '雇用形態' }]"
                ><el-select
                  size="large"
                  v-model="form.employType"
                  placeholder="雇用形態"
                  @change="handleFormChange"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in employTypeList"
                    :key="item.key"
                    :label="item.sectionNameI18n"
                    :value="item.key"
                  />
                </el-select>
              </el-form-item>
              <el-form-item v-if="form.currency" label="" prop="currency">
                <el-input
                  size="large"
                  v-model="form.currency"
                  disabled
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item
                label=""
                prop="annualSalary"
                :rules="[
                  { required: true, message: '年間給与を入力してください' },
                  { type: 'number', message: '有効な数字を入力してください。' },
                ]"
                ><el-input
                  size="large"
                  v-model.number="form.annualSalary"
                  placeholder="年間総支給額"
                  @input="handleFormChange"
                  style="width: 100%"
                />
              </el-form-item>
              <el-button class="calculator-submit" @click="fetchEmployCost"
                >コストを確認する</el-button
              >
            </el-form>
          </div>
        </div>
      </div>
    </header>
    <div id="calculate-result" class="calculator-container">
      <h2 class="result-title">世界各国の人件費をすばやく把握</h2>
      <p class="result-description">
        SmartDeerでは、採用候補地の選定をサポートし、税金・手数料・福利厚生などのコストを包括的に把握できます。
      </p>
      <div class="periodChange" v-if="status.showTable">
        <client-only>
          <el-dropdown @command="handlePeriodChange">
            <div class="el-dropdown-time" @click.prevent>
              <img width="12" src="~/assets/images/calculator/icon-choose-time.png" alt="">
              {{ period === 'year' ? '年' : '月' }}
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="year">年</el-dropdown-item>
                <el-dropdown-item command="month">月</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </client-only>
      </div>
      <table v-if="status.showTable" class="result-table">
        <tr>
          <td class="cost-name">{{ period === 'year' ? '年間総支給額' : '月間総支給額' }}</td>
          <td class="cost-amount">
            {{ form?.currency }}
            {{ period === 'year' ? formatNumber(calculateRes?.salary) : formatNumber(calculateRes?.salaryMonthly) }}
          </td>
        </tr>
        <tr>
          <td class="cost-name cost-position">
            <span class="icon">
              <ArrowDown
                v-if="!status.showTableDetails"
                @click="() => { status.showTableDetails = true; }"
              />
              <ArrowUp
                v-if="status.showTableDetails"
                @click="() => { status.showTableDetails = false; }"
              />
            </span>
            <span>{{ period === 'year' ? '企業負担コスト' : '企業負担コスト' }}</span>
          </td>
          <td class="cost-amount">
            {{ form?.currency }}
            {{ period === 'year' ? formatNumber(calculateRes.netTotalCosts) : formatNumber(calculateRes.netTotalCostsMonthly) }}
          </td>
        </tr>
        <template v-if="status.showTableDetails">
          <tr
            v-for="(cost, cost_index) in period === 'year' ? calculateRes?.costs : calculateRes?.costsMonthly"
            :key="cost_index"
          >
            <td class="cost-name-detail">{{ cost?.sectionNameI18n }}</td>
            <td class="cost-amount-detail">
              {{ form?.currency }} {{ formatNumber(cost?.costOrigin) }}
            </td>
          </tr>
        </template>
        <tr>
          <td class="cost-name">{{ period === 'year' ? '年間総支給額' : '月間総支給額' }}</td>
          <td class="cost-amount">
            {{ form?.currency }}
            {{ period === 'year' ? formatNumber(calculateRes?.totalCosts) : formatNumber(calculateRes?.totalCostsMonthly) }}
          </td>
        </tr>
      </table>
      <p v-if="status.showTable" class="result-notice">
        計算結果は、国の現地税金・コンプライアンスコストを基にした推定値です。個々の従業員データによって、実際の支給額や企業負担額が変動する場合があります。
      </p>
      <p class="entity-notice">
        SmartDeerは世界中に現地法人を保有しており、現地法人を設立することなく、各国で従業員を雇用できます。
      </p>
      <button class="calculator-contact" @click="handleContactUs">
        お問い合わせ
      </button>
    </div>
    <el-dialog v-model="status.showForm" title="" :width="354">
      <contact-us-form @submit="submitSuccess" lang="ja" />
    </el-dialog>
    <site-footer
      lang="ja"
      @contact-us="() => { status.showForm = true; }"
    />
  </div>
</template>

<script lang="ts" setup>
import { ElDialog, ElForm, ElFormItem, ElButton, ElInput, ElSelect, ElOption, ElMessage, ElDropdown, ElDropdownMenu, ElDropdownItem, ElCarousel, ElCarouselItem } from 'element-plus'
import langTool from '~/assets/utils/lang'
import {getQueryString} from "assets/utils";
import { reactive, ref, onMounted, onBeforeUnmount } from "vue";
// import costConfig from '~/assets/utils/global-cost-config';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'


definePageMeta({ layout: 'basic' })

// 获取运行时配置
const config = useRuntimeConfig()
const baseUrl = config.public.mobile_site_host || 'https://m.smartdeer.work'
const imageUrl = `${baseUrl}/images/calculator_banner.png`
const pageUrl = `${baseUrl}/ja/calculator`

useHead({
  htmlAttrs: { lang: 'ja' },
  title: '世界各国雇用コスト計算ツール - 海外従業員給与・福利厚生費シミュレーター | SmartDeer',
  meta: [
    { name: 'description', content: 'SmartDeerの無料グローバル雇用コスト計算ツールで、150カ国以上の雇用費用を簡単試算。給与、税金、社会保険料を含む総雇用コストを把握し、最適な採用判断を支援します。' },
    { name: 'keywords', content: '雇用コスト計算,海外給与計算,国際採用コスト,グローバル人件費,EOR費用計算,世界各国雇用費用,SmartDeer' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { name: 'author', content: 'SmartDeer' },

    // Open Graph標签
    { property: 'og:title', content: '世界各国雇用コスト計算ツール - 海外従業員給与・福利厚生費シミュレーター | SmartDeer' },
    { property: 'og:description', content: 'SmartDeerの無料グローバル雇用コスト計算ツールで、150カ国以上の雇用費用を簡単試算。給与、税金、社会保険料を含む総雇用コストを把握し、最適な採用判断を支援します。' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: pageUrl },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:image', content: imageUrl },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:image:type', content: 'image/png' },
    { property: 'og:image:alt', content: 'SmartDeer世界各国雇用コスト計算ツール' },
    { property: 'og:locale', content: 'ja_JP' },

    // Twitter Card標签
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: '世界各国雇用コスト計算ツール - 海外従業員給与・福利厚生費シミュレーター | SmartDeer' },
    { name: 'twitter:description', content: 'SmartDeerの無料グローバル雇用コスト計算ツールで、150カ国以上の雇用費用を簡単試算。給与、税金、社会保険料を含む総雇用コストを把握し、最適な採用判断を支援します。' },
    { name: 'twitter:image', content: imageUrl },
    { name: 'twitter:image:alt', content: 'SmartDeer世界各国雇用コスト計算ツール' }
  ],
  link: [
    // Canonical URL
    { rel: 'canonical', href: pageUrl },

    // Hreflang標签
    { rel: 'alternate', hreflang: 'zh', href: `${baseUrl}/zh/calculator` },
    { rel: 'alternate', hreflang: 'en', href: `${baseUrl}/en/calculator` },
    { rel: 'alternate', hreflang: 'ja', href: `${baseUrl}/ja/calculator` },
    { rel: 'alternate', hreflang: 'x-default', href: `${baseUrl}/calculator` }
  ],
  script: [
    // 结构化数据 - WebApplication
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "SmartDeer世界各国雇用コスト計算ツール",
        "description": "150カ国以上の雇用コストを無料で計算できるグローバル雇用費用シミュレーター",
        "url": pageUrl,
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD"
        },
        "provider": {
          "@type": "Organization",
          "name": "SmartDeer",
          "url": baseUrl
        },
        "featureList": [
          "150カ国以上の雇用コスト計算",
          "リアルタイム為替レート変換",
          "税金・社会保険料見積もり",
          "多通貨対応",
          "無料利用"
        ],
        "screenshot": {
          "@type": "ImageObject",
          "url": imageUrl
        }
      })
    }
  ]
})

const scrollItems = []

const status = reactive({
  showForm: false,
  showConsultantCode: false,
  showTable: false,
  showTableDetails: false
})

const formRef = ref()

const countryList = ref([])
const provinceList = ref([])
const allCityList = ref([]);
const cityList = ref([]);
const employTypeList = ref([])

const form = reactive({
  country: '',
  province: '',
  city: '',
  currency: '',
  employType: '',
  annualSalary: null
})

const calculateRes = ref({})

const period = ref('year');

function handleFormChange() {
  cityList.value = allCityList.value.filter(item => item.province === form.province);
  // fetchEmployCost();
}

function handleContactUs() {
  status.showForm = true
}

function formatNumber(number) {
  const formatter = new Intl.NumberFormat('ja-JP', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
  return formatter.format(number)
}

const host = "https://www-api.smartdeer.work/v1/website/function/runtime";
const hostTest = "https://www-api-test.smartdeer.work/v1/website/function/runtime";
const fetchCountry = async () => {
  const res = await fetch(host, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      'accept-language': 'ja'
    },
    body: JSON.stringify({
      functionKey: "search_website_smd_employer_cost_country_conf",
      params: {
        current: 1,
        size: 100,
        limit: 100,
        searchInfo: {
          searchItems: [],
          orders: [
            {
              key: "displayOrder",
              asc: "ASC",
            },
          ],
        },
      },
    }),
  });
  const {
    data: { dataInfo },
  } = await res.json();
  countryList.value = dataInfo || [];
};

const fetchCurrency = async (countryCode) => {
  const res = await fetch(`${host}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      'accept-language': 'ja'
    },
    body: JSON.stringify({
      functionKey: "x_website_employer_cost_country_conf",
      params: {
        countryCode,
      },
    }),
  });
  const { data } = await res.json();
  provinceList.value = data?.provinceConf || [];
  allCityList.value = data?.cityConf || [];
  employTypeList.value = data?.employTypeConf || [];
  form.currency = data?.currencyConf || "";
};

const fetchEmployCost = async () => {
  if (!form.country || !form.annualSalary) return;
  const params: any = {
    countryCode: form.country,
    employeeSalary: form.annualSalary,
    ...(provinceList.value.length && form.province
      ? { provinceCode: form.province }
      : {}),
    ...(employTypeList.value.length && form.employType
      ? { employType: form.employType }
      : {}),
    ...(cityList.value.length && form.city
      ? { cityCode: form.city }
      : {}),
  };
  const res = await fetch(host, {
    method: "POST",
    headers: { 
      "Content-Type": "application/json",
      'accept-language': 'ja'
    },
    body: JSON.stringify({
      functionKey: "x_website_cal_employer_cost",
      params,
    }),
  });
  const { data } = await res.json();
  calculateRes.value = {};
  calculateRes.value = data;
  status.showTable = true;
  scrollTo("#calculate-result");
};

const onCountryChange = async (val) => {
  form.province = "";
  form.employType = "";
  form.currency = "";
  provinceList.value = [];
  employTypeList.value = [];
  await fetchCurrency(val);
  handleFormChange();
};

onMounted(() => {
  window.addEventListener('scroll', scroll)

  const curScroll = getQueryString('scroll')
  if (curScroll) {
    scrollTo('#' + curScroll)
  }
  fetchCountry();
})

onBeforeUnmount(() => {
  window.removeEventListener('scroll', scroll)
})

function scrollTo(tag) {
  const ele = window.document.querySelector(tag)
  if (ele) window.scrollTo({
    top: ele.offsetTop,
    behavior: 'smooth'
  })
}

// 自定义指令
const vScrollShow = {
  mounted: (el, bindings) => {
    const delayOffset = bindings.value.delayOffset || 0
    scrollItems.push({ offsetTop: el.offsetTop + delayOffset, el: el })
  }
}

let timer = null
function scroll(e) {
  if (timer) return
  timer = setTimeout(() => {
    const offset = window.scrollY + window.innerHeight
    scrollItems.forEach((item, index) => {
      if (item.offsetTop < offset) {
        item.el.setAttribute('show', true)
        scrollItems.splice(index, 1)
      }
    })
    timer = null
  }, 30)
}

function switchLang(lang) {
  langTool.swithLang(lang)
}

function submitSuccess() {
  status.showForm = false
  ElMessage.success('お問い合わせありがとうございます。ご要望に応じて、担当者よりご連絡いたします。')
}

function handlePeriodChange(cmd) {
  period.value = cmd;
}
</script>

<style lang="scss" scoped>
header {
  background: #fff6ec;
  .header-banner {
    position: relative;
    width: 1204px;
    height: 620px;
    margin: 0 auto;
    // background-image: url("~/assets/images/calculator/banner.png");
    background-size: 1204px 590px;
    background-position: 0px;
    background-repeat: no-repeat;
  }

  .header-banner-text {
    position: absolute;
    left: 0;
    width: 641px;
    .header-title {
      font-size: 40px;
      font-weight: bold;
      margin-top: 190px;
    }
    .header-desc {
      font-size: 18px;
      line-height: 30px;
      letter-spacing: 1px;
      font-weight: 300;
    }
  }
  .header-form {
    position: absolute;
    top: 80px;
    right: 0;
    width: 405px;
    background: #FFF;
    border: 1px #EFF0F6 solid;
    border-radius: 22px;
    box-sizing: border-box;
    box-shadow: 0 10px 14px 0 rgba(74, 58, 255, 0.01),0 9px 26px 0 rgba(23, 15, 73, 0.05);
    padding: 40px 30px;

    .calculator-title {
      color: #170F49;
      text-align: center;
    }
    .calculator-desc {
      color: #6F6C90;
    }

    .calculator-submit {
      color: #FFF;
      width: 100%;
      height: 40px;
      line-height: 40px;
      border-radius: 20px;
      background: linear-gradient(259deg, #F54A25 -42%, #FFAB71 98%);
    }
  }
}
.el-dropdown-time{
  margin-top: 20px;
  display: flex;
  gap: 10px;
  .el-icon--right{
    width: 10px;
    height: 10px;
  }
}
.periodChange {
  display: flex;
  justify-content: flex-end;
  cursor: pointer;
}
.calculator-container {
  width: 1204px;
  margin: 0 auto;

  .result-title {
    margin-top: 50px;
    font-size: 36px;
    text-align: center;
  }

  .result-description {
    font-size: 18px;
    text-align: center;
    color: #6F6C90;
    line-height: 30px;
  }

  .result-table {
    border-collapse: collapse;
    width: 100%;
    margin-top: 20px;
  }

  .result-table tr, .result-table th, .result-table td {
    border: 1px #DBDEE5 solid;
  }

  .result-table td {
    width: 50%;
    font-size: 14px;
    height: 28px;
    padding: 24px 30px;
    color: #170F49;
    font-weight: 500;
  }

  .cost-name-detail, .cost-amount-detail {
    background: rgba(239, 239, 239, 0.3);
  }

  .cost-position {
    position: relative;
  }

  .cost-position .icon {
    cursor: pointer;
    position: absolute;
    display: block;
    width: 20px;
    height: 20px;
    right: 18px;
    top: 20px;
  }

  .result-notice {
    color: #6F6C90;
    font-size: 12px;
    line-height: 16px;
    margin-top: 15px;
  }

  .entity-notice {
    margin-top: 68px;
    font-size: 32px;
    font-weight: 400;
    color: #170F49;
  }

  .calculator-contact {
    display: block;
    cursor: pointer;
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    border-radius: 20px;
    width: 290px;
    height: 40px;
    color: #FFF;
    border: 0;
    background: linear-gradient(252deg, #F54A25 -45%, #FFAB71 98%);
    box-shadow: 0 4px 8px 0 rgba(255, 129, 42, 0.29);
    margin: 20px auto 75px;
  }
}

@media (max-width: 768px) {
  header {
    .header-banner {
      width: 90vw;
      min-width: 0;
      height: auto;
      background-size: cover;
      background-position: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0 10px;
      position: static;
    }
    .header-banner-text {
      position: static;
      width: 100%;
      margin-top: 40px;
      .header-title {
        font-size: 24px;
        margin-top: 0;
        text-align: center;
      }
      .header-desc {
        font-size: 14px;
        line-height: 22px;
        text-align: center;
      }
    }
    .header-form {
      position: static;
      width: 100%;
      margin: 24px auto;
      padding: 24px 10px;
      box-shadow: none;
      border-radius: 16px;
    }
  }
  .calculator-container {
    width: 90vw;
    min-width: 0;
    padding: 0 10px;
    box-sizing: border-box;
    .result-title {
      font-size: 22px;
    }
    .result-description {
      font-size: 14px;
    }
    .result-table td {
      font-size: 12px;
      padding: 12px 8px;
    }
    .entity-notice {
      font-size: 18px;
    }
    .calculator-contact {
      width: 100%;
      height: 40px;
      font-size: 14px;
      margin: 20px auto 40px;
    }
  }
}
</style>