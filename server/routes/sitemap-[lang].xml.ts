// 语言特定的 Sitemap 生成器

export default defineEventHandler(async (event) => {
  const lang = getRouterParam(event, 'lang')
  const config = useRuntimeConfig()
  // PC项目使用PC域名
  const baseUrl = config.public.pc_site_host || 'https://www.smartdeer.work'
  
  // 验证语言参数
  const supportedLanguages = ['zh', 'en', 'ja']
  if (!supportedLanguages.includes(lang)) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Language not supported'
    })
  }
  
  // 定义页面结构
  const pages = [
    { path: '', priority: '1.0', changefreq: 'daily' },
    { path: 'aboutus', priority: '0.8', changefreq: 'weekly' },
    { path: 'calculator', priority: '0.8', changefreq: 'weekly' },
    { path: 'countries', priority: '0.7', changefreq: 'weekly' },
    { path: 'marketing', priority: '0.7', changefreq: 'weekly' }
  ]
  
  // 根据语言添加特定内容
  const languageSpecificContent = {
    zh: [
      { path: 'articles/global-employment-china', priority: '0.7', changefreq: 'monthly' },
      { path: 'articles/china-labor-law', priority: '0.6', changefreq: 'monthly' },
      { path: 'mini_program', priority: '0.6', changefreq: 'weekly' }
    ],
    en: [
      { path: 'articles/global-employment-guide', priority: '0.7', changefreq: 'monthly' },
      { path: 'articles/eor-services', priority: '0.6', changefreq: 'monthly' }
    ],
    ja: [
      { path: 'articles/japan-employment-law', priority: '0.7', changefreq: 'monthly' },
      { path: 'articles/japan-payroll-guide', priority: '0.6', changefreq: 'monthly' }
    ]
  }
  
  // 合并页面列表
  const allPages = [...pages, ...(languageSpecificContent[lang] || [])]
  
  const currentDate = new Date().toISOString().split('T')[0]
  
  // 生成 XML
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allPages.map(page => `  <url>
    <loc>${baseUrl}/${lang}${page.path ? '/' + page.path : ''}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n')}
</urlset>`
  
  // 设置响应头
  setHeader(event, 'Content-Type', 'application/xml')
  setHeader(event, 'Cache-Control', 'max-age=3600')
  
  return sitemap
})
