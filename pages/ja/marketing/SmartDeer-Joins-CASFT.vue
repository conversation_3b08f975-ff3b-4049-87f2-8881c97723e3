<template lang="pug">
.contries-page
  site-header(lang="ja" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
    .countries-content
      h1.article-title SmartDeer Joined China Association of Foreign Service Trades
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="ja" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="ja" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: 'SmartDeer Joined China Association of Foreign Service Trades',
  ogDescription: 'With the accelerated pace of internationalization of Ch […]',
  ogSiteName: 'SmartDeer',
  description: 'With the accelerated pace of internationalization of Ch […]'
})
useHead({
  htmlAttrs: {
    lang: 'ja-JP'
  },
  title: 'SmartDeer Joined China Association of Foreign Service Trades'
})
const status = reactive({
  showForm: false
})
const langSimple = 'ja';
const pageTitle = 'SmartDeer Joined China Association of Foreign Service Trades';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/12/铜牌2.jpg';
const flagImage = '${COUNTRY_FLAT_IMAGE_URL}';
const htmlContent = '<p>With the accelerated pace of internationalization of Chinese enterprises, expanding HR-related services to overseas markets has become an important way for HR service providers to expand their services and find new business growth points. Recently, the Ninth General the Meeting of China Association of Foreign Service Trades (short name CAFST) and the International Business Exchange Meeting of HR Service Organizations were held in Beijing, and SmartDeer, as an industry leader in global HR services, was invited to attend the meeting and formally joined the association.</p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="1581" height="889" src="https://blog.smartdeer.work/wp-content/uploads/2024/12/铜牌-1.jpg" alt="" class="wp-image-1762"/></figure><p>Founded in 1989, China Association of Foreign Service Trades (short name CAFST) is a non government organization which was registered at the Ministry of Civil Affairs of China.By now, as a national association representing human resources service industry in China, CAFST has 160 members across the country. In 1999, approved by the Ministry of Foreign Affairs, CAFST joined Ciett (the former body of World Employment Confederation) as a member of national federations.  By such platform,CAFST plays animportant role on facilitating exchange and cooperation between Chinese human resources service companies and global peers.</p><figure class="wp-block-gallery has-nested-images columns-default is-cropped wp-block-gallery-1 is-layout-flex wp-block-gallery-is-layout-flex"><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="4000" height="2666" data-id="1764" src="https://blog.smartdeer.work/wp-content/uploads/2024/12/卢美延.jpg" alt="" class="wp-image-1764"/></figure><figure class="wp-block-image size-large"><img loading="lazy" decoding="async" width="4000" height="2667" data-id="1765" src="https://blog.smartdeer.work/wp-content/uploads/2024/12/外服2.jpg" alt="" class="wp-image-1765"/></figure></figure><p>SmartDeer, founded in 2021, is a one-stop &#8220;HR service and SaaS&#8221; platform dedicated to global recruitment and employment solutions. Incubated by Trustbridge Partners, with investments led by Welight Capital, WeWork, and Hash Global. SmartDeer is dual-headquartered in Hong Kong and Singapore, providing us with a strategic global advantage while ensuring compliance with local data storage and processing regulations. In addition to subsidiaries and branches in regions like the United States, the United Kingdom, the UAE, Saudi Arabia, Australia, Japan, South Korea, Thailand, Malaysia, Indonesia, the Philippines, Vietnam, Mainland China，Mexico and Brazil.Our services, delivered through our self-operated network and partners, span over 150 countries and regions worldwide.</p>';
function submitSuccess() {
  ElMessage.success('お問い合わせを受け付けました。できるだけ早くご連絡いたします。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>