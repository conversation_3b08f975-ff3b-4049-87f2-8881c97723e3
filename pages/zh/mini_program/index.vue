<template lang="pug">

</template>

<script lang="ts" setup>
definePageMeta({ layout: 'basic' })

useHead({ htmlAttrs: { lang: 'zh-CN' }, title: 'SmartDeer - 全球雇&全球招，出海企业的一站式解决方案专家' })

onMounted(() => {
  const cozeWebSDK = new CozeWebSDK.WebChatClient({
    config: {
      botId: '7439335660751716386'
    },
    ui: {
      base: {
        icon: "https://static.smartdeer.com/bot_logo.png",
        layout: "mobile",
        zIndex: 1000
      },
      header: {
        isNeedClose: false
      },
      asstBtn: {
        isNeed: false
      },
      footer: {
        isShow: false,
        expressionText: 'Powered by SmartDeer'
      },
      chatBot: {
        title: 'SmartDeer 出海服务',
        uploadable: false
      }
    }
  });
  cozeWebSDK.showChatBot();
})

</script>

<style lang="scss" scoped>

</style>