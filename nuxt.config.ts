/*
 * @Author: sx <EMAIL>
 * @Date: 2022-12-14 16:02:20
 * @LastEditors: sx <EMAIL>
 * @LastEditTime: 2023-01-13 12:59:49
 * @FilePath: \bpo-website-mobile\nuxt.config.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// https://v3.nuxtjs.org/api/configuration/nuxt.config

import ElementPlus from 'unplugin-element-plus/vite'

const env = process.env.BUILD_ENV || 'development'

const CONFIG = {
  test: {
    mobile_site_host: 'https://m-test.smartdeer.work',
    pc_site_host: 'https://www-test.smartdeer.work'
  },
  production: {
    mobile_site_host: 'https://m.smartdeer.work',
    pc_site_host: 'https://www.smartdeer.work'
  },
  development: {
    mobile_site_host: '',
    pc_site_host: ''
  }
}


export default defineNuxtConfig({
  // SEO 和性能优化配置
  experimental: {
    payloadExtraction: false // 提升性能
  },

  // 全局 SEO 设置
  app: {
    head: {
      htmlAttrs: {
        lang: 'zh-CN'
      },
      title: 'SmartDeer - 全球招聘雇佣，海外人力资源一站式服务平台',
      titleTemplate: '%s | SmartDeer',
      meta: [
        // 基础 meta 标签
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' },
        { name: 'format-detection', content: 'telephone=no' },

        // SEO meta 标签
        { name: 'description', content: 'SmartDeer提供全球招聘、海外雇佣、薪酬管理等一站式人力资源服务，覆盖150+国家，助力企业全球化扩张。专业的EOR、PEO、全球薪酬解决方案。' },
        { name: 'keywords', content: '全球招聘,海外雇佣,人力资源外包,EOR,PEO,全球薪酬,海外用工,国际招聘,SmartDeer,人力资源服务,全球化扩张,跨境用工,海外人事,国际人才' },
        { name: 'author', content: 'SmartDeer' },
        { name: 'robots', content: 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1' },

        // 百度 SEO 优化
        { name: 'baidu-site-verification', content: 'baidu_verification_placeholder' },
        { name: 'applicable-device', content: 'mobile' },
        { name: 'renderer', content: 'webkit' },

        // 必应 SEO 优化
        { name: 'msvalidate.01', content: '1C90A015812D60F281F96A31ACC73A2D' },
        { name: 'msapplication-TileColor', content: '#2d89ef' },

        // Open Graph 标签
        { property: 'og:type', content: 'website' },
        { property: 'og:site_name', content: 'SmartDeer' },
        { property: 'og:title', content: 'SmartDeer - 全球招聘雇佣，海外人力资源一站式服务平台' },
        { property: 'og:description', content: 'SmartDeer提供全球招聘、海外雇佣、薪酬管理等一站式人力资源服务，覆盖150+国家，助力企业全球化扩张。' },
        { property: 'og:url', content: CONFIG[env].mobile_site_host || 'https://m.smartdeer.work' },
        { property: 'og:image', content: 'https://m.smartdeer.work/images/tg_banner.png' },
        { property: 'og:image:width', content: '1390' },
        { property: 'og:image:height', content: '781' },
        { property: 'og:image:type', content: 'image/png' },
        { property: 'og:image:alt', content: 'SmartDeer - 全球人力资源服务平台' },
        { property: 'og:locale', content: 'zh_CN' },

        // Twitter Card 标签
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:site', content: '@smartdeer' },
        { name: 'twitter:creator', content: '@smartdeer' },
        { name: 'twitter:title', content: 'SmartDeer - 全球招聘雇佣，海外人力资源一站式服务平台' },
        { name: 'twitter:description', content: 'SmartDeer提供全球招聘、海外雇佣、薪酬管理等一站式人力资源服务，覆盖150+国家，助力企业全球化扩张。' },
        { name: 'twitter:image', content: 'https://m.smartdeer.work/images/tg_banner.png' },
        { name: 'twitter:image:alt', content: 'SmartDeer - 全球人力资源服务平台' }
      ],
      link: [
        // Favicon 和图标
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'canonical', href: CONFIG[env].mobile_site_host || 'https://m.smartdeer.work' },

        // DNS 预解析
        { rel: 'dns-prefetch', href: '//hm.baidu.com' },
        { rel: 'dns-prefetch', href: '//www.googletagmanager.com' },
        { rel: 'dns-prefetch', href: '//bat.bing.com' },

        // 预连接重要资源
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' }
      ]
    },
    buildAssetsDir: '/static/'
  },
  runtimeConfig: {
    public : CONFIG[env]
  },
  build: {
    transpile: ['element-plus/es'],
  },
  vite: {
    plugins: [ElementPlus({
      useSource: true
    })],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "~/assets/styles/element.scss" as *;`,
        },
      },
    },
  }
})