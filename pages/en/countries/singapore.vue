<!--
 * @Author: sx <EMAIL>
 * @Date: 2023-01-10 09:55:23
 * @LastEditors: sx <EMAIL>
 * @LastEditTime: 2023-01-13 17:33:23
 * @FilePath: \bpo-website-pc\pages\zh\contries\canada.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template lang="pug">
.contries-page
  site-header(lang="en" @contact-us="status.showForm = true")

  .contries
    .contries-banner
      .banner
        figure
          img(src="~/assets/images/countries/singapore_banner.webp")
      .flag
        figure
          img(src="~/assets/images/countries/singapore.png")
      .contact-us
        button(@click="status.showForm = true") Contact Us    
    
    .contries-content
      h1 Employer of Record in Singapore
      .desc Build Distributed Teams With SmartDeer
      
      h3 Compensation and Benefit
      .list
        .list-item Working Hours
          .list-item.wrap Standard working hours are Up to 9 hours per day or 44 hours a week. The standard workweek is from Monday to Friday.
        .list-item Public Holidays
          .list-item Annual Leave
            .list-item.wrap   Full-time employees are entitled to 7 days of paid time off (PTO) a year. PTO accrues monthly 0.58 days per month. Employees are eligible for annual leave after 3 months of work. Employees are entitled to one additional day for each year of service until reaching 14 days.
          .list-item Sick Leave
            .list-item   Employees are entitled to paid sick leave for up to 14 days provided they have worked for at least 3 months. The number of sick days varies depending on the length of service. Employees will receive 100% of their salary while on sick leave paid by the employer.
            .list-item   Employees are also entitled to paid hospitalization leave for up to 60 days provided they have worked for at least 3 months. The number of sick days varies depending on the length of service. Employees will receive 100% of their salary while on sick leave paid by the employer
          .list-item Maternity Leave
            .list-item   Pregnant employees who have worked for 3 consecutive months are entitled to 16 weeks of paid leave. 4 weeks must be taken before the child's birth and 8 weeks after the birth. The remaining 4 weeks can be used as the employee wishes. The employee will receive 100% of their average salary during this period, and the employer will be responsible for the first 8 weeks and Social Security for the second 8 weeks.
          .list-item Paternity Leave
            .list-item   Employees who have worked for 3 consecutive months are entitled to 2 weeks of paid paternity leave. The 2 weeks must be taken continuously anytime within the 16 weeks of maternity leave. The employee will receive their normal salary capped at $2500 per week and Social Security will be responsible for this pay.
            .list-item   In Singapore, no law covers parental leave. However, fathers are entitled to share 4 weeks of the mother's 16 weeks of maternity leave. The employee will receive their normal salary capped at $2500 per week
          .list-item Family Leave
            .list-item   Employees who have worked for 3 consecutive months are entitled to 2 weeks of paid paternity leave. The 2 weeks must be taken continuously anytime within the 16 weeks of maternity leave. The employee will receive their normal salary capped at $2500 per week and Social Security will be responsible for this pay.
            .list-item   In Singapore, no law covers parental leave. However, fathers are entitled to share 4 weeks of the mother's 16 weeks of maternity leave. The employee will receive their normal salary capped at $2500 per week
      
      h3 Tax
      .list
        .list-item Individual Income Tax Rate
          .list-item The individual income tax ranges from 0% to 22%
        .list-item VAT Rate: 7%
        .list-item Retirement Age: 63 

      h3 Policy of Termination & Severance
      .list
        .list-item The minimum notice period is 1 day and will be increased according to the length of the employment.
        .list-item 1 day if the length of service is less than 26 weeks
        .list-item 1 week if the length of service is between 26 weeks and 2 years
        .list-item 2 weeks if the length of service is between 2 years and 5 years
        .list-item 4 weeks if services if longer than 5 years
        
      h3 Main Types of VISAs
      .list
        .list-item Work permits are required in Singapore; however, the type of permit required is based on the person’s skills. A person with a degree, professional qualifications, or specialist skill will need an Employment Pass to work in Singapore.
        .list-item In contrast, a Work Permit is required for other skilled or unskilled foreign workers. The Singapore Ministry of Manpower (MOM) is responsible for issuing of all types of Employment Passes.
        .list-item Different types of Employment Passes may be issued depending on the applicant’s salary, but the basic procedures remain the same. An Employment Pass is set by the individual and the company they work for. If an Employment Pass holder wants to change jobs, the employer will have to cancel the existing EP, and the new employer will have to apply for a new one. The Employment Pass must also be cancelled if the holder ceases employment in Singapore or obtains Permanent Resident status.


  site-footer(lang="en" @contact-us="status.showForm = true")
  .contact-form
    client-only
      el-dialog(v-model="status.showForm" title="" :width="354")
        contact-us-form(lang="en" @submit="submitSuccess")

</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
definePageMeta({ layout: 'basic' })
useHead({ htmlAttrs: { lang: 'zh-CN' }, title: 'Employer of Record in Singapore' })

const status = reactive({
  showForm: false
})

function switchLang(lang) {
  langTool.swithLang(lang, '/aboutus')
}

function submitSuccess() {
  ElMessage.success('Submit success!')
  status.showForm = false
}

function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>

<style lang="scss" scoped>
@import './common.scss';
</style>