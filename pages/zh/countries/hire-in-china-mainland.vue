<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在中国大陆雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在中国大陆雇佣',
  ogDescription: '基础信息 货币：人民币 (CNY) 官方语言：中文 薪资周期：每月 SmartDeer提供的本地福利 企业支付 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 货币：人民币 (CNY) 官方语言：中文 薪资周期：每月 SmartDeer提供的本地福利 企业支付 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在中国大陆雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在中国大陆雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/04/北京.jpeg';
const flagImage = '';
const htmlContent = '<h2 class="wp-block-heading">基础信息</h2><p>货币：人民币 (CNY)</p><p>官方语言：中文</p><p>薪资周期：每月</p><h2 class="wp-block-heading">SmartDeer提供的本地福利</h2><ul><li>社会保险：养老保险、医疗保险、失业保险、工伤保险、生育保险</li></ul><p>企业支付部分：五险均需缴纳，费用国家统筹</p><p>个人支付部分：仅支付养老保险、医疗保险、失业保险，费用进个人账户</p><ul><li>住房公积金：</li></ul><p>企业和个人均需支付，费用进个人账户</p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>