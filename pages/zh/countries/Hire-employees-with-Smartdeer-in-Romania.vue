<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在罗马尼亚雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在罗马尼亚雇佣',
  ogDescription: '基础信息 首都：布加勒斯特 Bucharest 时区：GMT+2 语言：罗马尼亚语 货币代码：RON 人力资源 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：布加勒斯特 Bucharest 时区：GMT+2 语言：罗马尼亚语 货币代码：RON 人力资源 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在罗马尼亚雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在罗马尼亚雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/罗马尼亚-FI.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/罗马尼亚-国旗.jpg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：布加勒斯特 Bucharest</p><p>时区：GMT+2</p><p>语言：罗马尼亚语</p><p>货币代码：RON</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading">雇佣合同</h3><p>&nbsp;&nbsp;合同必须用罗马尼亚语书写，可以是双语。必须书面签署双方。</p><p>&nbsp;&nbsp;合同必须包括：</p><ul><li>当事人身份</li><li>工作地点和总部</li><li>职位和工作描述</li><li>绩效评估标准</li><li>工作条件（符合当地法律）</li><li>合同日期</li><li>雇佣合同类型（固定期限、临时）</li><li>年假</li><li>通知期</li><li>工资</li><li>工作时间</li><li>试用期的持续时间</li><li>终止详细信息</li></ul><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>&nbsp;&nbsp;试用期不是强制性的。没有最低试用期。最长试用期取决于雇佣类型。</p><p>&nbsp;&nbsp;对于无固定期限的合同：</p><ul><li>行政职位 &#8211; 90天</li><li>管理和协调职位 &#8211; 120天</li><li>有残疾的员工 &#8211; 30天</li></ul><p>&nbsp;&nbsp;固定期限合同：</p><ul><li>小于3个月的合同 &#8211; 5天</li><li>3至6个月的合同 &#8211; 15天</li><li>长于6个月的合同 &#8211; 30天</li><li>长于6个月的合同（管理职位的员工） &#8211; 45天</li></ul><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工作时间为每天8小时，每周40小时。标准工作周为周一至周五。</p><p>加班工资是强制性的。可以以财务形式或带薪休假形式进行补偿。标准工作时间以外的时间被视为加班时间。员工每月最多可以工作32个小时的加班。对于额外的小时数，员工的支付为：</p><ul><li>工作日：每小时平均工资的150％</li><li>周末：每小时平均工资的200％</li></ul><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>最低工资为每月3300罗马尼亚列伊。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><ol start="1"><li>  雇主成本通常估计为员工工资的6.25％。</li></ol><ul><li>劳动保险费 &#8211; 2.25％</li><li>残疾费用 &#8211; 4.0％</li><li>远程劳动补偿金 &#8211; 400罗马尼亚列伊*</li><li>IT评估费（一次性费用） &#8211; 300美元</li><li>对于没有工作满整个月的员工进行比例调整。</li></ul><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading">终止原因</h3><p>罗马尼亚的解雇可能很复杂。罗马尼亚雇主不能随意解雇员工，解雇必须有正当理由。</p><p>符合条件的终止包括：</p><ul><li>与员工相关的原因：<ul><li>纪律失职（在严重违反工作纪律规定或个人劳动合同、集体谈判协议或内部规定中更重复的违规情况下）</li><li>身体/精神能力不足（由相关医疗机构确定）</li><li>专业不足</li><li>因被拘留或在家中监禁超过30天</li></ul></li><li>与员工无关的原因<ul><li>在重组后因经济、财务或组织原因裁员</li></ul></li><li>双方协议</li><li>试用期</li><li>合同到期</li></ul><p>注意：员工不能在休假期间、临时伤残期间、隔离期间或怀孕期间被解雇，如果雇主知道怀孕的情况。</p><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>最短通知期为20天，并根据解雇原因而定。</p><ul><li>解雇 &#8211; 最少20天</li><li>员工辞职<ul><li>行政职位 &#8211; 最多20天</li><li>管理/协调职位 &#8211; 最多45天</li></ul></li><li>双方协议终止 &#8211; 雇主可以放弃通知期</li></ul><p>根据罗马尼亚立法，解雇日期与员工的最后工作日期不同（通常最后工作日期是解雇日期前一天）。</p><p>在解雇/辞职期间，员工在其最后工作日的同一个工资周期内支付。例如，如果他们的最后工作日是6月29日，则解雇日期将是6月30日。</p><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>只有在雇佣协议或集体协议中事先提到，或者已经事先协商过的情况下，才需要支付离职补偿金。</p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading">薪资支付</h3><p>雇主必须至少每月向雇员支付工资，支付日期由雇佣合同规定。工资必须通过银行直接转账支付给雇员。</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>雇主无需向员工提供分项工资单，尽管这是惯例。工资发放时，员工将通过电子邮件收到工资单作为付款证明，如雇佣合同中约定的那样。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职和兼职员工每年有权获得20天带薪休假（PTO）。带薪休假按照每月1.66天的速度累积。员工一旦累积了假期，就有资格休假。</p><p>法律上，员工必须每年至少连续休假10个工作日。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>员工每年有权获得最多90天的带薪病假。病假的前5个日历日由雇主支付，额外的病假日由雇主支付并由国家健康保险基金偿付。这种假期根据疾病支付不同比例的薪资。付款比例由医生或医院出具的医学证明中列明的疾病代码和百分比代码确定。最低薪资支付为工资的75％，最高为工资的100％。</p><p>病假可以在一个日历年内最多延长183天，从疾病首日起计算，经社会保险专家医生批准。</p><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>在过去1年内有6个月工作经验的怀孕员工有权休126天的带薪休假。员工必须在孩子出生后至少休息42天。产假由国家社会保障基金支付，支付比例为员工过去12个月的平均收入的85％。</p><p>员工不能延长休假。</p><p>员工有权获得2年的无薪育婴假，而有残疾子女的父母有权获得3年的无薪育婴假。如果母亲没有享受此类育婴假，或者她已经使用了一部分假期，则该假期授予父亲。</p><p>员工在育婴假期间将获得国家的育儿津贴，该津贴为员工在孩子出生前2年内过去12个连续月所获得的平均净收入的85％。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading">个人所得税</h3><p>个人所得税率为10％。个人所得税按统一税率计算。</p><h3 class="wp-block-heading">社会保险</h3><p>所有持有居留许可的就业和自雇人士均须按照财政立法的要求缴纳社会保障金（养老金和健康保险），才能获得保险。</p><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>&nbsp; 日期</td></tr><tr><td>New Year&#8217;s Day</td><td>1.1</td></tr><tr><td>New Year Holiday</td><td>1.2</td></tr><tr><td>Epiphany</td><td>1.6</td></tr><tr><td>Synaxis of St.John the Baptist</td><td>1.7</td></tr><tr><td>Union of the Romanian Principalities</td><td>1.24</td></tr><tr><td>Labor Day</td><td>&nbsp; 5.1</td></tr><tr><td>Orthodox Good Friday</td><td>&nbsp; 5.3</td></tr><tr><td>Orthodox Easter Sunday</td><td>&nbsp; 5.5</td></tr><tr><td>Orthodox Easter Monday</td><td>&nbsp; 5.6</td></tr><tr><td>Children&#8217;s Day</td><td>&nbsp; 6.1</td></tr><tr><td>Orthodox Whit Sunday</td><td>6.23</td></tr><tr><td>Orthodox Whit Monday</td><td>6.24</td></tr><tr><td>Assumption Day</td><td>8.15</td></tr><tr><td>Feast of Saint Andrew</td><td>11.3</td></tr><tr><td>Great Union Day</td><td>12.1&nbsp;</td></tr><tr><td>Christmas Day</td><td>12.25&nbsp;</td></tr><tr><td>Second Day of Christmas</td><td>12.26&nbsp;</td></tr></tbody></table></figure><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>