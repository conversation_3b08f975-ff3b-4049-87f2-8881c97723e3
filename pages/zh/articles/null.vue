<template lang="pug">
.countries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")

    .countries-content
      h1.article-title 中国医药企业如何在巴西市场大展拳脚？
      div(v-html="htmlContent")
  site-footer(lang="zh" @contact-us="status.showForm = true")
  .contact-form
    client-only
      el-dialog(v-model="status.showForm" title="" :width="354")
        contact-us-form(lang="zh" @submit="submitSuccess")
  .fixed-contact(@click="status.showForm = true")
    img(src="~/assets/images/aboutus/ic_contact_us.png")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({ layout: 'basic' })
useSeoMeta({
  ogTitle: '中国医药企业如何在巴西市场大展拳脚？',
  ogDescription: '中国医药企业在巴西市场会应对严峻的挑战，巴西的医药市场在全球20大经济体中排名第10位，中国和巴西的医药市场都 […]',
  ogSiteName: 'SmartDeer',
  description: '中国医药企业在巴西市场会应对严峻的挑战，巴西的医药市场在全球20大经济体中排名第10位，中国和巴西的医药市场都 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '中国医药企业如何在巴西市场大展拳脚？'
})

const status = reactive({
  showForm: false
})

const pageTitle = '中国医药企业如何在巴西市场大展拳脚？';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/08/1b812aaac8e6841f84fa6a9002a78857.jpg';
const flagImage = '';
const htmlContent = '<p>  中国医药企业在巴西市场会应对严峻的挑战，巴西的医药市场在全球20大经济体中排名第10位，中国和巴西的医药市场都非常庞大且充满潜力，两国的医药需求都在快速增长，中国企业有全球领先的制造能力和成本优势，我们smartdeer合作过的企业在在巴西目前都非常稳定。当然，进入巴西市场也面临一些十分棘手挑战，一旦踩坑，不只是会影响业务的开展，严重会导致出海业务停摆。第一，巴西的药品税负高，本地销售成本也高。第二，巴西药品准入法规和审批流程严格，中国企业需要相当长的时间和前期投入资金来进入这个市场。第三，巴西是国际巨头药企的传统市场，市场竞争非常激烈，中国企业需要不断创新和提升产品质量，在竞争中脱颖而出。但是我们smartdeer也有应对的措施，我们为中国企业的出海提供涵盖150多个国家地区的一站式人力资源服务、风险预警服务，确保用工制度符合当地法规，对当地政策的更新做实时更新，以确保其符合最新的法律法规要求，对企业的出海保驾护航工作我们有丰富的经验。中国企业想要进入巴西市场，有几种主要路径，一、跨境并购。二、合资和战略合作。三、技术转移。四、直接投资和建厂，在巴西直接投资建厂，利用本地资源和市场优势，我们smartdeer根据企业需求，提供合规管理咨询服务，确保在企业0-1出海阶段，能够一帆风顺。朋友们，中国医药企业在快速发展，出海也势在必行，早一点出发起航，便会早一点达到目的地。</p>';

function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
  status.showForm = false
}

function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>

<style lang="scss">
@import './article-detail.scss';
</style>