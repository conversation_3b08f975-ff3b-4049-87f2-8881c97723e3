<!--
 * @Author: xu.sun <EMAIL>
 * @Date: 2022-11-09 18:58:50
 * @LastEditors: xu.sun <EMAIL>
 * @LastEditTime: 2022-11-18 17:04:43
 * @FilePath: /bpo-website-mobile/app.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template lang="pug">
div
  NuxtLayout
    NuxtPage
</template>

<script lang="ts" setup>
import { ID_INJECTION_KEY } from "element-plus"
import { switchingPlatforms } from '~/assets/utils'
import { onMounted } from 'vue'

onMounted(() => {
  switchingPlatforms()
})

defineNuxtPlugin((nuxtApp) => {
  const elementPlusPlugin = {};
  nuxtApp.vueApp.provide(ID_INJECTION_KEY, {
    prefix: Math.floor(Math.random() * 10000),
    current: 0,
  })
})
</script>

<style lang="scss">
@import '~/assets/styles/reset.scss';
</style>
  