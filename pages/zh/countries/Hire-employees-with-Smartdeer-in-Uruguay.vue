<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在乌拉圭雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在乌拉圭雇佣',
  ogDescription: '基础信息 首都：蒙得维的亚 Montevideo 时区：GMT-3 语言：西班牙语 货币代码：UYU 人力资源 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：蒙得维的亚 Montevideo 时区：GMT-3 语言：西班牙语 货币代码：UYU 人力资源 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在乌拉圭雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在乌拉圭雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/06/2.1.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/06/2f3f-a28f25704a9090d968ee36f997e3a594.jpg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：蒙得维的亚 Montevideo</p><p>时区：GMT-3</p><p>语言：西班牙语</p><p>货币代码：UYU</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h2 class="wp-block-heading has-large-font-size">雇佣合同</h2><p>合同必须是西班牙语的，可以是双语的。必须是书面形式，并由双方签字。</p><p>合同必须包括:</p><p>-名字</p><p>-开始日期</p><p>-聘用期限</p><p>-职位描述</p><p>-终止条件</p><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>试用期不是强制性的。试用期最长为90天。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工作时间为每天8小时，每周40小时。标准的工作周是从星期一到星期五。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>乌拉圭的最低工资19364比索。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>雇主成本一般估计为雇员工资的15.85%:</p><p>每月的成本</p><p>-社会保障(BPS) &#8211; 7.5%*</p><p>-健康保险(FONASA)——5%</p><p>-额外基金(FRL) &#8211; 0.1%</p><p>-担保基金(FGL) &#8211; 0.025%</p><p>-意外保险- 3.23%</p><p>*最高薪金为UYU 236,309。</p><p>年度成本</p><p>-第13个月工资- 1个月基本工资</p><p>-第14个月工资(假期奖金)-每休假一天，相当于上一年度一天的净补偿金</p><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading has-large-font-size">终止原因</h3><p>解雇必须尊重复杂的规则和雇员所在国家的规则。离职事宜始终由雇主与主要利益相关者共同处理。它可能包括特别费用以及针对特定终止案件所需或建议的步骤。</p><p>在乌拉圭，终止很复杂。在乌拉圭，试用期以外的雇主不能随意解雇员工，必须有正当理由才可以解雇。</p><p>合规终止包括:</p><p>-由员工自愿</p><p>-经双方同意</p><p>-由雇主单方面根据:</p><p>-试用期</p><p>-客观依据</p><p>-纪律处分解雇</p><p>-不适合工作导致的表现</p><p>-在合同期满之前</p><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>通知期不是强制性的，而是取决于双方在雇佣协议中达成的协议。</p><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>在乌拉圭，所有在试用期内未因违反纪律或定期合同到期而被解雇的雇员都有权获得遣散费。遣散费是每服务一年一个月的工资，最多六个月的基本补偿。</p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading has-large-font-size">薪资支付</h3><p>每月一次</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>无明确规定</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职和兼职员工每年都有20个工作日的带薪休假。PTO按每月1.67天计算。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>雇员有权享受无限制的带薪病假。前3天是无薪的，被视为合格期。第三天以后，由社保支付70%的工资。</p><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>怀孕的员工有14周的带薪假期。在孩子出生前必须提前42天，但如果出示医疗证明，并且在怀孕期间没有因医疗原因缺勤，则可以推迟到出生前两周。员工将获得过去六个月平均工资的100%，社会保障将负责支付这笔工资。</p><p>员工有权在出生后的头六个月内每周工作50%。父母双方都可以休育儿假。在此期间，社会保险将支付平均工资的100%。产假不能延长。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading has-large-font-size">个人所得税</h3><p>个人所得税从0%到36%不等。所得税是按累进税率计算的。许多其他因素可能影响总体比率，如家庭状况和子女数量。</p><h2 class="wp-block-heading has-large-font-size">社会保险</h2><p>-社会保障</p><p>-工人赔偿保险</p><p>-私人医疗保健- Unisure(可选)</p><p>-私人医疗保健-安联(可选)</p><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>日期</td></tr><tr><td>New Year&#8217;s Day&nbsp;&nbsp;&nbsp;&nbsp;</td><td>1.1</td></tr><tr><td>Labour Day</td><td>5.1</td></tr><tr><td>Constitution Day</td><td>7.18</td></tr><tr><td>Independence Day</td><td>8.25</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr></tbody></table></figure>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>