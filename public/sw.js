// Service Worker for SmartDeer Website
// 提供缓存优化和离线支持

const CACHE_NAME = 'smartdeer-v1.0.0'
const STATIC_CACHE = 'smartdeer-static-v1.0.0'
const DYNAMIC_CACHE = 'smartdeer-dynamic-v1.0.0'

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/zh/',
  '/en/',
  '/ja/',
  '/favicon.ico',
  '/images/logo.png',
  '/images/tg_banner.png',
  '/static/css/main.css',
  '/static/js/main.js'
]

// 需要缓存的动态资源模式
const CACHE_PATTERNS = [
  /^https:\/\/fonts\.googleapis\.com/,
  /^https:\/\/fonts\.gstatic\.com/,
  /\.(?:png|jpg|jpeg|svg|gif|webp)$/,
  /\.(?:css|js)$/
]

// 不需要缓存的资源模式
const NO_CACHE_PATTERNS = [
  /^https:\/\/hm\.baidu\.com/,
  /^https:\/\/www\.googletagmanager\.com/,
  /^https:\/\/bat\.bing\.com/,
  /\/api\//,
  /\/admin\//
]

// 安装 Service Worker
self.addEventListener('install', event => {
  console.log('Service Worker installing...')
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('Caching static assets...')
        return cache.addAll(STATIC_ASSETS)
      })
      .then(() => {
        console.log('Static assets cached successfully')
        return self.skipWaiting()
      })
      .catch(error => {
        console.error('Failed to cache static assets:', error)
      })
  )
})

// 激活 Service Worker
self.addEventListener('activate', event => {
  console.log('Service Worker activating...')
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            // 删除旧版本的缓存
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        console.log('Service Worker activated')
        return self.clients.claim()
      })
  )
})

// 拦截网络请求
self.addEventListener('fetch', event => {
  const { request } = event
  const url = new URL(request.url)
  
  // 跳过非 GET 请求
  if (request.method !== 'GET') {
    return
  }
  
  // 跳过不需要缓存的资源
  if (NO_CACHE_PATTERNS.some(pattern => pattern.test(request.url))) {
    return
  }
  
  // 处理静态资源
  if (STATIC_ASSETS.includes(url.pathname)) {
    event.respondWith(
      caches.match(request)
        .then(response => {
          return response || fetch(request)
        })
    )
    return
  }
  
  // 处理图片资源
  if (request.destination === 'image') {
    event.respondWith(
      caches.match(request)
        .then(response => {
          if (response) {
            return response
          }
          
          return fetch(request)
            .then(fetchResponse => {
              // 只缓存成功的响应
              if (fetchResponse.status === 200) {
                const responseClone = fetchResponse.clone()
                caches.open(DYNAMIC_CACHE)
                  .then(cache => {
                    cache.put(request, responseClone)
                  })
              }
              return fetchResponse
            })
            .catch(() => {
              // 如果网络请求失败，返回默认图片
              return caches.match('/images/placeholder.png')
            })
        })
    )
    return
  }
  
  // 处理字体资源
  if (CACHE_PATTERNS.some(pattern => pattern.test(request.url))) {
    event.respondWith(
      caches.match(request)
        .then(response => {
          if (response) {
            return response
          }
          
          return fetch(request)
            .then(fetchResponse => {
              if (fetchResponse.status === 200) {
                const responseClone = fetchResponse.clone()
                caches.open(DYNAMIC_CACHE)
                  .then(cache => {
                    cache.put(request, responseClone)
                  })
              }
              return fetchResponse
            })
        })
    )
    return
  }
  
  // 处理页面请求 - 网络优先策略
  if (request.mode === 'navigate') {
    event.respondWith(
      fetch(request)
        .then(response => {
          // 缓存成功的页面响应
          if (response.status === 200) {
            const responseClone = response.clone()
            caches.open(DYNAMIC_CACHE)
              .then(cache => {
                cache.put(request, responseClone)
              })
          }
          return response
        })
        .catch(() => {
          // 网络失败时从缓存返回
          return caches.match(request)
            .then(response => {
              return response || caches.match('/')
            })
        })
    )
    return
  }
})

// 处理消息
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME })
  }
  
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      )
    }).then(() => {
      event.ports[0].postMessage({ success: true })
    })
  }
})

// 后台同步（如果支持）
if ('sync' in self.registration) {
  self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
      event.waitUntil(
        // 执行后台同步任务
        console.log('Background sync triggered')
      )
    }
  })
}

// 推送通知（如果需要）
self.addEventListener('push', event => {
  if (event.data) {
    const data = event.data.json()
    const options = {
      body: data.body,
      icon: '/images/icon-192x192.png',
      badge: '/images/badge-72x72.png',
      data: data.url
    }
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    )
  }
})

// 处理通知点击
self.addEventListener('notificationclick', event => {
  event.notification.close()
  
  if (event.notification.data) {
    event.waitUntil(
      clients.openWindow(event.notification.data)
    )
  }
})
