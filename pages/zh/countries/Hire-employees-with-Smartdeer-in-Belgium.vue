<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在比利时雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在比利时雇佣',
  ogDescription: '基础信息 首都：布鲁塞尔 Brussels 时区：GMT+1 语言：荷兰语、法语、德语 货币代码：EUR 人力 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：布鲁塞尔 Brussels 时区：GMT+1 语言：荷兰语、法语、德语 货币代码：EUR 人力 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在比利时雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在比利时雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/比利时-FI.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/比利时国旗.png';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：布鲁塞尔 Brussels</p><p>时区：GMT+1</p><p>语言：荷兰语、法语、德语</p><p>货币代码：EUR</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading">雇佣合同</h3><p>合同必须始终以书面形式书写，并明确约定的雇佣类型、试用期、限制性契约和通知。由于比利时各地区的语言多样性，编写雇佣合同的语言使用受到特定规则的约束。</p><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>对于比利时，没有且不允许试用期。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>比利时的标准工作时间是每周38小时。</p><p>超过每周标准工作时间的工时将获得加班工资。某些职位和职位可能适用特殊情况。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>比利时的最低工资为每月1,593.81欧元。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>&nbsp;&nbsp;雇主成本通常估计为员工工资的27.78%：</p><ul><li>社会保障 &#8211; 27.28%</li><li>工伤保险 &#8211; 0.50%</li></ul><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading">终止原因</h3><p>终止必须遵守复杂的规则和员工所在国的规定。离职始终由雇主与主要利益相关者处理。它可能包括特定终止案例的临时费用以及所需或建议的步骤。</p><p>雇主可以通过提供适当的法定通知来终止劳动合同，或者支付通知期间的补偿金，此时合同立即终止。</p><p>员工如果认为解雇是不合理的，可以向法院上诉。如果法院认为解雇是不公平的，可能需要支付额外的补偿金。</p><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>对于持续3个月的雇佣关系，最低通知期为2周。根据雇佣时长，所需通知期会增加。</p><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>除某些集体解雇外，员工在终止雇佣关系时通常没有法定权利获得遣散费。</p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading">薪资支付</h3><p>一月一次</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>每笔工资必须附有书面工资单，详细说明总薪酬的计算方式。在某些行业，工资单的内容由 CBA 确定。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>每周工作38小时的员工将享有每年20个工作日的带薪休假。</p><p>对于兼职员工，计算将按比例进行。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>员工有权在生病休假的头30天内获得由雇主支付的全薪。30天后，员工通常有权从政府获得疾病津贴。</p><p>所有病假必须由医疗专业人员出具证明。</p><p>产假&amp;育儿假</p><p>怀孕的员工有权休产假15周。这段假期可以在分娩前6周开始，至少在宝宝出生后休9周。员工在产假前30天内可获得等于其平均工资的82%的津贴，从第31天开始直到假期结束为止的津贴为其平均工资的75%。</p><p>工作父母如果在过去的15个月内与同一雇主有至少12个月的服务，则有权享受4个月的育婴假。此权利适用于每个个体。父母可以共同休假或在不同时间休假。此假期必须在孩子满12岁之前（如果孩子有残疾，则为21岁）休完。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading">个人所得税</h3><p>个人所得税税率从25%到50%不等。个人所得税按照渐进税率计算。多个附加因素可能会影响总税率，例如家庭状况和子女数量。</p><h3 class="wp-block-heading">社会保险</h3><figure class="wp-block-table"><table><tbody><tr><td></td><td>EE Contribution(%)&nbsp;</td><td>ER Contribution (%)</td></tr><tr><td>Old age,disability and survivors</td><td>7.50</td><td>8.86</td></tr><tr><td>Sickness &amp;Maternity</td><td>&nbsp; 一</td><td></td></tr><tr><td>-Medical Benefits</td><td>3.55</td><td>3.80</td></tr><tr><td>-Cash Benefits &amp;Disability Pensions</td><td>1.15</td><td>2.20</td></tr><tr><td>-Maternity Benefits</td><td></td><td>0.15</td></tr><tr><td>Work Injury</td><td>&nbsp; 一</td><td>0.32</td></tr><tr><td>Occupational Disease</td><td></td><td>1.00</td></tr><tr><td>Family Allowances</td><td></td><td>7.00</td></tr><tr><td>Unemployment</td><td>0.87</td><td>1.60</td></tr><tr><td>TOTAL</td><td>13.07</td><td>24.93</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>日期</td></tr><tr><td>New Year&#8217;s Day</td><td>&nbsp;1.1</td></tr><tr><td>Easter Sunday</td><td>&nbsp;3.31</td></tr><tr><td>Easter Monday</td><td>&nbsp;4.1</td></tr><tr><td>Labour Day</td><td>&nbsp;5.1</td></tr><tr><td>Ascension Day</td><td>&nbsp;5.9</td></tr><tr><td>Whit Sunday</td><td>&nbsp;5.19</td></tr><tr><td>Whit Monday</td><td>&nbsp;5.20</td></tr><tr><td>Belgian National Day</td><td>&nbsp;7.21</td></tr><tr><td>Assumption Day</td><td>&nbsp;8.15</td></tr><tr><td>All Saint&#8217;s Day</td><td>&nbsp;11.1</td></tr><tr><td>Armistic Day</td><td>&nbsp;11.11</td></tr><tr><td>Christmas Day</td><td>&nbsp;12.25</td></tr></tbody></table></figure><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>