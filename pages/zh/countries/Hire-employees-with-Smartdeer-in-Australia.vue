<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在澳大利亚雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在澳大利亚雇佣',
  ogDescription: '基础信息 首都：堪培拉 Canberra 时区：GMT+10 语言：英语 货币代码：AUD 人力资源制度概况 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：堪培拉 Canberra 时区：GMT+10 语言：英语 货币代码：AUD 人力资源制度概况 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在澳大利亚雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在澳大利亚雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/04/澳大利亚Banner.webp';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/04/澳大利亚国旗.png';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：堪培拉 Canberra</p><p>时区：GMT+10</p><p>语言：英语</p><p>货币代码：AUD</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><p>雇佣合同</p><p>&#8211; 雇主和雇员的姓名和详细信息</p><p>&#8211; 员工的出生日期</p><p>&#8211; 职位</p><p>&#8211; 职责和责任</p><p>&#8211; 工作地点</p><p>&#8211; 雇佣状态（全职、兼职或临时工）</p><p>&#8211; 合同的起止日期</p><p>&#8211; 工资水平</p><p>&#8211; 付款日期</p><p>&#8211; 工作时间</p><p>&#8211; 休假权利</p><p>&#8211; 终止雇佣条款</p><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>雇主可以决定试用期的长度，从几周到几个月不等，通常为3-6个月；在此期间，全职和兼职员工有权累积和使用带薪休假，如年假和病假。如果员工未通过试用期，雇主必须通知他们。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工作时间为每天7.6小时，每周38小时（每年1967小时）。标准工作周从星期一到星期五。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>23.23每小时</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>雇主成本通常约为员工工资的16.4-18.5%。但是，总费用可能会因员工居住的州而异。</p><p>&#8211; 养老基金 &#8211; 11.0%（2024/07/01后增长为11.5%）：外国工人离开澳大利亚时，有资格要求将他们的养老金作为离境澳大利亚养老金支付退回</p><p>&#8211; 工伤保险 &#8211; 1-2%</p><p>&#8211; 薪资税 &#8211; 4.95-6.85%</p><p>如果向员工提供医疗福利，将会收取强制性的边际福利税。边际福利税金额为健康保险费的47%，乘以1.8868的毛额率。</p><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading">终止原因</h3><p>&#8211; 雇员自愿</p><p>&#8211; 双方协商一致</p><p>&#8211; 雇主单方面基于：</p><p>&nbsp;&nbsp;&#8211; 试用期</p><p>&nbsp;&nbsp;&#8211; 客观原因</p><p>&nbsp;&nbsp;&#8211; 纪律性解雇</p><p>&nbsp;&nbsp;&#8211; 工作不适合绩效</p><p>&nbsp;&nbsp;&#8211; 合同到期</p><p>如果员工认为自己被以苛刻、不公正或不合理的方式解雇，他们可以申请不公平解雇。员工符合以下条件可提出不公平解雇申请：在雇主处工作至少六个月，或者如果雇主是小型企业，则至少工作了12个月。雇主必须在解雇之日起21天内向澳大利亚劳动委员会提交申请。</p><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>最短通知期为1周，根据雇佣年限将延长。</p><p>&#8211; 雇佣不满1年 &#8211; 1周通知</p><p>&#8211; 1 &#8211; 3年雇佣 &#8211; 2周通知</p><p>&#8211; 3 &#8211; 5年雇佣 &#8211; 3周通知</p><p>&#8211; 5年以上雇佣 &#8211; 4周通知</p><p>&nbsp;&nbsp;如果员工年龄超过45岁，并且在同一雇主处工作了2年，则有资格获得额外1周通知。</p><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>雇员的离职补偿将取决于在公司工作的年限。</p><p>&#8211; 在公司工作不满1年的员工不获得离职补偿。</p><p>&#8211; 1-2年工作经验 = 4周离职补偿</p><p>&#8211; 2-3年工作经验 = 6周离职补偿</p><p>&#8211; 3-4年工作经验 = 7周离职补偿</p><p>&#8211; 4-5年工作经验 = 8周离职补偿</p><p>&#8211; 5-6年工作经验 = 10周离职补偿</p><p>&#8211; 6-7年工作经验 = 11周离职补偿</p><p>&#8211; 7-8年工作经验 = 13周离职补偿</p><p>&#8211; 8-9年工作经验 = 14周离职补偿</p><p>&#8211; 超过10年工作经验 = 16周离职补偿</p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h2 class="wp-block-heading has-large-font-size">薪资支付&nbsp;</h2><p>一月一次</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>工资单必须在支付后的一个工作日内发放。工资单可以是电子版或纸质版，其中包含每个工资周期的支付、扣除和退休金缴纳等详细信息。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>&#8211; 全职和兼职员工有权享受四周带薪年假。这也被称为假期工资。轮班工人可能有资格再额外获得一周年假。雇主可以提供超过NES规定的最低年假。</p><p>&#8211; 年假以小时计算，从雇佣的第一天开始累积，包括试用期。未使用的年假必须年年累积。在解雇雇佣合同时可以支付未使用的年假。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>&#8211; 员工每年可享受最多10天带薪个人/照顾者假，以及每年两天的无薪照顾者假。奖励、注册协议或合同中可以规定不同的带薪病假和照顾者假权益，但这些权益不得低于上述最低标准。</p><p>&#8211; 病假和照顾者假以天为单位累积，从员工入职第一天开始计算。年度结束时，员工的余额必须结转到下一年。</p><p>&#8211; 未使用的病假和照顾者假在解雇雇佣关系时不予支付。</p><h2 class="wp-block-heading has-large-font-size">丧假</h2><p>&#8211; 所有员工有权利享受两天带薪的同情假（丧假），可在直系亲属去世或患有生命威胁性疾病或受伤时休假。</p><p>&#8211; 员工可以连续休假两天，或将假期拆分为两个单独的一天。员工和雇主也可以互相同意将假期分开取。</p><p>&#8211; 雇主可以要求提供证据，例如葬礼文件、死亡通知或法定声明。</p><h2 class="wp-block-heading has-large-font-size">育儿假</h2><p>&#8211; 育儿假（产假和陪产假）可在员工生育、员工的配偶或事实伴侣生育，或员工收养16岁以下的孩子时休假。员工必须至少在雇主处工作了12个月，才能获得假期。</p><p>&#8211; 育儿假由政府根据国家最低工资的周薪率支付，最长可达20周。</p><p>&#8211; 员工有权享受长达12个月的无薪育儿假，并有权申请额外的12个月假期。收养孩子的员工有权享受2天无薪的收养前假期，以参加任何相关的面试或检查。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading has-large-font-size"> 个人所得税</h3><p>个人在澳大利亚的税务责任由其居住状态确定。从每年的7月1日持续到次年的6月30日征税。</p><p>Residents居民：</p><p>澳大利亚的居民通常是指打算在澳大利亚逗留超过六个月的个人，或者在澳大利亚有收入超过六个月的个人。澳大利亚居民必须在年度澳大利亚税务申报表上声明在澳大利亚和国际上获得的所有收入。通常可以提供外国所得税抵免，以减少相同收入的澳大利亚税，使个人避免双重征税。</p><figure class="wp-block-table"><table><tbody><tr><td><strong></strong><strong>Income thresholds(AUD) 收入标准</strong><strong></strong></td><td><strong>Rate of tax 税率</strong><strong></strong></td><td><strong>Tax payable 应纳税款</strong><strong></strong></td></tr><tr><td>0 &#8211; 18,200</td><td>0%</td><td>0</td></tr><tr><td>18,201 &#8211; 45,000</td><td>19%</td><td>超过$18,200至$45,000的部分按照19%交税</td></tr><tr><td>45,001 &#8211; 120,000</td><td>32.50%</td><td>超过$45,000至$120,000的部分按照32.5%交税</td></tr><tr><td>120,001 &#8211; 180,000</td><td>37%</td><td>超过$120,000至$180,000的部分按照37%交税</td></tr><tr><td>180,001以上</td><td>45%</td><td>超过$180,000以上的部分按照45%交税</td></tr></tbody></table></figure><p>Non-residents非居民：</p><p>澳大利亚的非居民通常是指在澳大利亚逗留时间少于六个月的人。非居民应对从澳大利亚直接或间接来源的任何收入进行评估，这受到双重税务协定的影响。</p><figure class="wp-block-table"><table><tbody><tr><td><strong></strong><strong>Income thresholds(AUD) 收入标准</strong><strong></strong></td><td><strong>Rate of tax 税率</strong><strong></strong></td><td><strong>Tax payable 应纳税款</strong><strong></strong></td></tr><tr><td>0 &#8211; 120,000</td><td>32.5%</td><td>32.50%</td></tr><tr><td>120,001 &#8211; 180,000</td><td>37%</td><td>$39,000加上超过$120,000部分的37%</td></tr><tr><td>180,001以上</td><td>45.00%</td><td>$61,200加上超过$180,000部分的45%</td></tr></tbody></table></figure><p>Temporary Residents临时居民：</p><p>澳大利亚的临时居民是指持有特定临时签证并符合其他规定情况的人。临时居民应对在抵达澳大利亚后从所有来源获得的就业收入以及所有来源于澳大利亚的投资收入进行评估，这受到双重税务协定的影响。</p><h2 class="wp-block-heading has-large-font-size">2024年法定假期<img loading="lazy" decoding="async" width="12" height="10" src="https://weboffice.feishu-3rd-party-services.com/api/v3/office/copy/RDVDQjBONGExRUdKSVpna01KUEdMVitPYUliRFM3UFhuZ01zdVZhVGcwYlpmMTl6TzVtRVlPZGRyZ253MWNNNDdORTJNcDEvd1hXYW5wdzNQVlpIT0ZUQXRTYzQ3NGdlc2hEdjIrb2xQN0MxUGdaMjlSRWpDODJkak1BRkFWb0J2SmFtM094bTQ0ZytESTYyM2FRVk1rUDdvbVgrOHpkWGZnRkNNditKZGI1K3p6b2tHWU8rc2NVZm9sekVlZEtTaFZWTW5VenAvS1k0QVRhc0RGdW9PZ3FKay8vUkJLc2pXemYzY1RvVk1JSXM4MVFTWkdkdnF1azhteG5BQ0xGVnF3N0Y0NmZISmdwZDdpSnJoT21BTzdoTHFscG01R1U9/attach/object/3cc91908811c73239758f09f7644d48cad335546?"></h2><figure class="wp-block-table"><table><tbody><tr><td><strong>Occasion 假期</strong><strong></strong></td><td><strong>Date 日期</strong><strong></strong></td></tr><tr><td>New Year&#8217;s Day</td><td>1.1</td></tr><tr><td>Australia Day</td><td>1.26</td></tr><tr><td>Good Friday</td><td>3.29</td></tr><tr><td>Easter Saturday (except TAS &amp; WA)</td><td>3.30</td></tr><tr><td>Easter Sunday (except NT, SA &amp; TAS)</td><td>3.31</td></tr><tr><td>Easter Monday</td><td>4.1</td></tr><tr><td>Anzac Day</td><td>4.25</td></tr><tr><td>King&#8217;s Birthday (except QLD &amp; WA)</td><td>6.10</td></tr><tr><td>Labour Day (ACT. NSW &amp; SA)</td><td>10.7</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr><tr><td>Boxing Day (except SA)</td><td>12.26</td></tr></tbody></table></figure>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>