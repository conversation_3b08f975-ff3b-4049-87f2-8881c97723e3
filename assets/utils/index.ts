/*
 * @Author: xu.sun <EMAIL>
 * @Date: 2022-11-18 16:19:48
 * @LastEditors: sx <EMAIL>
 * @LastEditTime: 2023-01-13 16:10:48
 * @FilePath: /bpo-website-mobile/assets/utils/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// 平台切换
export function switchingPlatforms() {
  const route = useRoute()

  if (route.path === '/') return

  const config = useRuntimeConfig().public
  const result = window.matchMedia("(max-width: 700px)")

  if (result.matches) return

  const host = config.pc_site_host

  if (!host) return

  const page = `${host}${route.path}`
  location.replace(page)
}

// 获取url 参数
export function getQueryString(name: string) {
    const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    const r = window.location.search.substring(1).match(reg);
    if (r != null) {
      return unescape(r[2]);
    }
    return null;
  }