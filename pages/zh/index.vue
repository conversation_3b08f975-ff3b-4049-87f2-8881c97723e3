<template lang="pug">

mixin header
  header
    //- .header-background
    //-   .decoration
    site-header(lang="zh" source="home" @contact-us="status.showForm = true")

    .header-content

      .header-banner
        .header-banner-text
          .slogon 全球招&全球雇
          h1.site-title 全球人力资源一站式服务
          .desc 
            .desc-text 招纳全球优秀人才，处理全球雇佣合规与薪酬发放问题，提供专业的人力资源一站式服务解决方案。
        .header-banner-image
          figure
            img(src="~/assets/images/index/globle-min.webp")

mixin customer
  section.customer
    .section-title  
      h2 服务全球海量客户
      p Serve Global Customers
    CustomerList

mixin service
  section.service
    .section-title
      h2 全球人力服务 & 全球 HR Saas
      p Our Solutions & Global Saas
    .service-list
      .service-item(id="service-recruitment" v-scroll-show="{ delayOffset: 100 }")
        .figure-area
          figure
            img(src="~/assets/images/index/recruitment.webp" alt="全球人才招聘服务 Global Recruitment")
        .service-content
          .service-title
            h3 全球人才招聘服务
            p Global Recruitment
          .service-desc
            p 在目标市场有用人需求，期待寻求合适人才，部署全球团队。
            p 以丰富人才库储备为基础，多年经验猎头团队实现精准人才搜寻与交付，24小时响应机制高效服务客户，专业研发团队提供技术支持。
            button.service-contact-button(@click="status.showForm=true") 咨询详情
              ArrowRight(class="inline-arrow")
      .service-item(id="service-eor" v-scroll-show="{ delayOffset: 100 }")
        .figure-area
          figure
            img(src="~/assets/images/index/eor.webp" alt="全球名义雇主服务 Employer of Record")
        .service-content
          .service-title
            h3 全球名义雇主服务
            p Employer of Record
          .service-desc
            p 出海目标国家暂无法律主体，需要合规雇佣全职员工，省去境外设立与经营的费用，最低成本部署全球团队。
            p 可为全职用工提供合规、签证、签约入职、薪酬福利、团队管理、税务保险等全生命周期服务，企业系统平台帮助实现报表数据统计及自动化流程管理。
            button.service-contact-button(@click="status.showForm=true") 咨询详情
              ArrowRight(class="inline-arrow")
      .service-item(id="service-contractor" v-scroll-show="{ delayOffset: 100 }")
        .figure-area
          figure
            img(src="~/assets/images/index/contractor.webp" alt="全球灵活用工服务 Independent Contractor")
        .service-content
          .service-title
            h3 全球灵活用工服务
            p Independent Contractor
          .service-desc
            p 在目标国家市场有短期灵活用工诉求，暂无法律主体，雇佣独立承包商提供短期服务能力。
            p 可提供合规手续与模板化合同，通过受监督认可的方式与海外员工完成100%符合当地法律要求的线上签约，签约前对员工进行合规背景审查，最大化地避免了入职过程中可能产生的摩擦与后续的法律风险，并可支持计薪发薪服务，换汇费用透明。
            button.service-contact-button(@click="status.showForm=true") 咨询详情
              ArrowRight(class="inline-arrow")
      .service-item(id="service-peo" v-scroll-show="{ delayOffset: 100 }")
        .figure-area
          figure
            img(src="~/assets/images/index/peo.webp" alt="全球人力资源服务 Human Resource Outsourcing")
        .service-content
          .service-title
            h3 全球人力资源服务
            p Human Resource Outsourcing
          .service-desc
            p 在目标市场已建有法律主体，期待将部分或全部人力资源职能外包给专业团队运营。
            p 可提供计薪发薪、个税管理、强制保险、员工福利、员工入离职调转等服务，支持可选定制化解决方案，高效、低成本、准确地帮助企业应对不同国家或地区的法律法规。
            button.service-contact-button(@click="status.showForm=true") 咨询详情
              ArrowRight(class="inline-arrow")
      .service-item(id="service-fintech" v-scroll-show="{ delayOffset: 100 }")
        .figure-area
          figure
            img(src="~/assets/images/index/fintech.png" alt="金融科技驱动的全球薪资解决方案 FinTech-Driven Global Payroll Solutions")
        .service-content
          .service-title
            h3 金融科技驱动的全球薪资解决方案
            p FinTech-Driven Global Payroll Solutions
          .service-desc
            p SmartDeer 平台支持处理超过 150 种货币的薪资支付，可高效地将收入打款至员工和承包商的银行账户，显著降低国际转账成本。同时，SmartDeer 具备强大的批量支付功能、具有竞争力的汇率和强大的锁汇能力，有效降低汇率波动对薪资支出的影响。此外，SmartDeer 在合规与税务管理方面的专业能力，确保了全球化人力资源运营的顺畅，是国际雇佣解决方案的可靠合作伙伴。
            button.service-contact-button(@click="status.showForm=true") 咨询详情
              ArrowRight(class="inline-arrow")
      .service-item(id="service-saas" v-scroll-show="{ delayOffset: 100 }")
        .figure-area
          figure
            video(id="video-player" class="video-js" preload="auto" controls="true" poster="~/assets/images/index/video-bg.jpg")
              source(src="https://static.smartdeer.com/Global_HR_SaaS.mp4", type="video/mp4")
        .service-content
          .service-title
            h3 全球 HR Saas
            p Global HR SaaS
          .service-desc
            p SmartDeer 通过“人力服务+Saas系统”的模式为企业全球化布局提供专业的一体化解决方案，SmartDeer Global HR Saas 主要为企业实现全球人力资源数智化管理。
            p 让全球员工、全球 HR 和 Line Manager, 可以高效地处理人事流程和相关事务, 让全球人力资源核心数据可以统一维护管理。
            button.service-contact-button(@click="status.showForm=true") 咨询详情
              ArrowRight(class="inline-arrow")
mixin advantage
  section.advantage
    .section-title
      h2 核心优势
      p Why choose us?

    .advantage-list()
      .advantage-item()
        figure.advantage-icon-area
          img(src="~/assets/images/index/icon-global.svg" alt="覆盖150+个国家")
        .advantage-title 全球覆盖
        .advantage-content 服务网点覆盖150+个国家

      .advantage-item()
        figure.advantage-icon-area
          img(src="~/assets/images/index/icon-professional-team.svg" alt="覆盖150+个国家")
        .advantage-title 专业合规团队
        .advantage-content 确保全球政策，用户数据合规

      .advantage-item()
        figure.advantage-icon-area
          img(src="~/assets/images/index/icon-service.svg" alt="72小时全中文响应")
        .advantage-title 24小时全天响应
        .advantage-content 7x24小时响应中英文双语服务

      .advantage-item()
        figure.advantage-icon-area
          img(src="~/assets/images/index/icon-price.svg" alt="极具竞争优势的价格")
        .advantage-title 优势的价格
        .advantage-content 极具竞争优势的服务费用


mixin lifecycle
  section.lifecycle
    .section-title
      h2 全生命周期管理
      p Full-Life Cycle Management

    .lifecycle-list
      .lifecycle-list-container
        .lifecycle-item(:data-nth="lifecycleItems[3]")
          figure
            img(src="~/assets/images/index/recruitment.svg")
          .title 招聘

        .lifecycle-item(:data-nth="lifecycleItems[2]")
          figure 
            img(src="~/assets/images/index/compliance.svg")
          .title 合规

        .lifecycle-item(:data-nth="lifecycleItems[1]")
          figure 
            img(src="~/assets/images/index/contract.svg")
          .title 签约

        .lifecycle-item(:data-nth="lifecycleItems[0]")
          figure 
            img(src="~/assets/images/index/on-boarding.svg")
          .title 入职

        .lifecycle-item(:data-nth="lifecycleItems[6]")
          figure 
            img(src="~/assets/images/index/management.svg")
          .title 管理

        .lifecycle-item(:data-nth="lifecycleItems[5]")
          figure 
            img(src="~/assets/images/index/payment.svg")
          .title 支付

        .lifecycle-item(:data-nth="lifecycleItems[4]")
          figure 
            img(src="~/assets/images/index/off-boarding.svg")
          .title 离职

mixin process
  section.process
    .section-title
      h2 服务流程
      p Service Process
    .process-list
      .process-item
        .process-head
          .process-head-wrapper
            figure
              img(src="~/assets/images/index/recriument.png")
            .num 01
        .process-content
          h3.title 团队覆盖全球、涉及领域广泛、实现本地化招聘
          .desc 80+猎头顾问，覆盖中国大陆、东南亚、北美、中东等多国家与地区，涉及IT、Saas、游戏、智能制造等行业，擅长运营、BD、sales、工程师等热门岗位，在全球实现本地化招聘业务。

      .process-item
        .process-head
          .process-head-wrapper
            .num 02
            figure
              img(src="~/assets/images/index/compliance.webp")
        .process-content
          h3.title 提供签证服务、合规背景调查、办理合规手续
          .desc 与客户达成合规合作协议，为员工提供本地化合规模板，并办理各类合规手续，最大程度避免入职过程中可能产生的摩擦与后续的法律风险，支持签证办理、背景调查等可选服务。

      .process-item
        .process-head
          .process-head-wrapper
            figure
              img(src="~/assets/images/index/contract.webp")
            .num 03
        .process-content
          h3.title 选择合约模板、自助创建合同、3分钟完成签订
          .desc 根据客户情况与用人需求，选择多国合规合约模板，敲击键盘，3分钟完成起草到签约，专业团队帮您审核，支持电子签名效力，在线完成签约过程。

      .process-item
        .process-head
          .process-head-wrapper
            .num 04
            figure
              img(src="~/assets/images/index/progress.webp")
        .process-content
          h3.title 提供个人资料、线上自动审核、员工自助入职
          .desc 邀请员工登录系统，上传个人资料，平台自动审核，线上完成自助入职流程，保障员工按时入职。

      .process-item
        .process-head
          .process-head-wrapper
            figure
              img(src="~/assets/images/index/staff.webp")
            .num 05
        .process-content
          h3.title 在线合约管理、在线员工档案、考勤假期统计
          .desc 跟进合约签订进度，查看员工档案，实现人事变动，支持多种打卡方式和审批流程自动化，实时管控员工考勤与休假情况。


      .process-item
        .process-head
          .process-head-wrapper
            .num 06
            figure
              img(src="~/assets/images/index/pay.webp")
        .process-content
          h3.title 薪酬项目维护、定期计薪发薪、缴纳保险福利
          .desc 报销、津贴、费用自助申请，完成线上审批，根据各国各地区复杂的计薪管理需求和法律法规，自动生成账单，支持多币种薪酬的支付，换汇费用透明。


      .process-item
        .process-head
          .process-head-wrapper
            figure
              img(src="~/assets/images/index/dimission.png")
            .num 07
        .process-content
          h3.title 协调离职事宜、清算离职费用、办理离职手续
          .desc 客户提前特定日期提交离职申请，平台协调员工的离职事宜并清算离职费用，按照当地规定办理合规的离职手续。

mixin solutionCase
  section.solution(id="service-solution")
    .section-title
      h2 行业案例
      p Solution
    .solution-list
      .solution-item.case1-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-figure
            figure
              img(src='~/assets/images/index/case1.png')
          .solution-content(:class="{collapsed: !solutionExpandStatus.s1}")
            .solution-title
              h3 一家中国领先的 ICT 解决方案供应商 —— 全球团队扩展与管理的最佳实践
            .solution-desc(:style="getSolutionDescStyle('s1')")
              h4 背景与痛点
              p 该企业自 2020 年启动全球化业务扩展，目前已进入 30 多个国家和地区，并有以下具体需求：
              ol
                li #[strong 全球化雇佣需求：]覆盖 16 个国家的 EOR 雇佣服务和签证办理，5 个国家的 HRO 服务。
                li #[strong 多国雇主成本测算：]需要精准计算多国雇主成本，并根据最新政策提供专业建议。
                li #[strong 政策咨询与高效支持：]需持续关注各国的雇佣政策变化并快速响应。
              h4 主要挑战包括：
              ul
                li 事务性工作繁琐：海外 HR 团队需处理大量日常咨询，涉及复杂问题且需快速反馈。
                li 签证办理难度大：目标国家多为高难度签证地区，流程繁琐，风险高。
                li 报销数据压力大：业务部门的报销需求数据量庞大且集中，给 HR 和财务团队带来极大压力。
              h4 解决方案
              p SmartDeer 针对企业的全球化需求及挑战，制定了综合解决方案：
              ol
                li #[strong EOR 和 HRO 服务：]通过 SmartDeer 的全球网络，为 16 个国家提供 EOR 服务和签证办理支持，并在 5 个国家提供本地化 HRO 服务，包括薪资管理、员工福利和劳动合同管理。
                li #[strong 实时政策咨询：]通过 SmartDeer 专业团队，为企业提供多国雇佣政策和税务法规的实时咨询，并协助快速调整雇佣策略。
                li #[strong 签证办理支持：]SmartDeer 提供从文件准备到递交的全流程支持，覆盖高难度国家的签证申请，显著提升通过率。
                li #[strong 数据自动化管理：]通过 SmartDeer 平台整合报销数据流，自动化处理数据提交和分析，降低了手动操作的时间成本。
                li #[strong 高效响应机制：]为企业配备专属客户经理和 24/7 支持团队，确保复杂问题高效解决，咨询反馈时间缩短至 24 小时内。
              h4 成果
              ul
                li #[strong 全球业务高效推进：]成功协助企业在 16 个国家快速雇佣团队并完成签证办理需求，确保各市场顺利运营。
                li #[strong 时间成本降低 40%：]通过自动化和智能化管理，大幅减少 HR 团队在事务性工作的投入。
                li #[strong 签证成功率提升至 95%：]在高难度国家的签证办理中，SmartDeer 提供的支持显著提高了申请成功率。
                li #[strong 报销效率提升 50%：]通过报销数据的自动化处理，业务部门数据流更加高效。
              h4 客户评价
              p “SmartDeer 在全球化扩张中为我们提供了全方位的支持，无论是雇佣服务还是签证办理，他们都展现出了极高的专业性和效率。SmartDeer 是我们出海业务不可或缺的战略合作伙伴。” —— 客户国际业务负责人
            .solution-expand
              button.solution-toggle(@click="toggle('s1')")
                span(v-if="!solutionExpandStatus.s1") 展开详情
                span(v-else) 收起详情
                ArrowDown(v-if="!solutionExpandStatus.s1" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
      .solution-item.case2-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-figure
            figure
              img(src='~/assets/images/index/case2.png')
          .solution-content(:class="{collapsed: !solutionExpandStatus.s2}")
            .solution-title
              h3 一家中国知名游戏开发公司 —— 从 0 到 1 的日本市场成功之路
            .solution-desc(:style="getSolutionDescStyle('s2')")
              h4 背景
              p 这家中国大陆年轻的游戏开发公司于 2022 年注册日本实体，2023 年正式开始开拓日本市场。这是企业首次出海，对日本市场充满不确定性。在业务初期，企业不仅需要快速构建本地团队，还需应对日本严格的劳动法规和复杂的人事管理要求。
              h4 主要挑战：
              ul
                li #[strong 缺乏专业 HR 团队支持：]由于企业首次进入日本市场，在短时间内难以招聘到具备本地经验的专业 HR 团队。
                li #[strong 法律法规的复杂性：]日本劳动法规要求严格，企业需确保所有人事和劳务管理流程合规，以避免潜在的法律风险。
              h4 解决方案
              p SmartDeer 针对企业的初期需求和痛点，提供了全面且定制化的解决方案：
              ol
                li #[strong 专业全面的 HRO 服务：]SmartDeer 协助企业制定并备案员工管理制度，梳理入职流程，准备人事管理所需的各类文档，同时完善员工个人信息使用规则，确保所有流程合法合规。
                li #[strong 本地法律咨询与合规保障：]SmartDeer 的日本团队深入现场，协助企业处理劳务合同、薪资计算、社会保险等复杂事务，并实时提供劳动法规的专业建议。
                li #[strong 招聘与团队构建支持：]利用 SmartDeer 的本地网络资源，为企业快速招募合适人才，帮助企业顺利构建初期团队。
                li #[strong 定制化服务：]根据企业初创期需求，提供针对性的现场支持，确保企业从 0 到 1 的阶段性目标得以实现。
              h4 成果
              ul
                li #[strong 快速组建本地团队：]在 3 个月内完成了日本团队的招聘和管理，确保业务顺利启动。
                li #[strong 实现合规运营：]所有人事和劳务流程符合日本法律法规，避免了潜在的法律风险。
                li #[strong 时间与成本优化：]帮助企业节省了 40% 的时间和 HR 成本，将更多精力投入到核心业务中。
              h4 客户评价
              p “SmartDeer 的团队不仅为我们提供了标准化的解决方案，更深入了解了我们作为初次出海企业的需求。他们的专业服务让我们在日本市场从 0 到 1 的过程变得高效且稳妥。” —— 客户 HR 负责人
            .solution-expand
              button(@click="toggle('s2')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s2") 展开详情
                span(v-else) 收起详情
                ArrowDown(v-if="!solutionExpandStatus.s2" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
      .solution-item.case3-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-figure
            figure
              img(src='~/assets/images/index/case3.png')
          .solution-content(:class="{collapsed: !solutionExpandStatus.s3}")
            .solution-title
              h3 一家领先的智能机器人解决方案提供商 —— 全球化扩展中的薪资管理支持
            .solution-desc(:style="getSolutionDescStyle('s3')")
              h4 背景
              p 作为全球智能机器人领域的领军企业，该公司近年来在全球范围内快速扩张业务，服务覆盖多个行业和地区。伴随着业务的拓展，其国际团队的规模不断扩大，但在全球薪资管理方面面临以下挑战：
              ol
                li #[strong 多国薪资政策差异：]不同国家的薪资法规、税务政策和员工福利要求复杂多样。
                li #[strong 支付流程分散：]国际薪资发放需要协调多种支付渠道，增加了时间和运营成本。
                li #[strong 团队管理压力：]人力资源部门需应对大规模、多地域的薪资计算与数据管理，负担沉重。
              h4 主要挑战：
              ul
                li #[strong 合规性保障：]确保薪资支付符合各国法律法规，避免合规风险。
                li #[strong 成本控制：]需要降低多国汇率波动和国际转账费用对薪资成本的影响。
                li #[strong 效率提升：]优化全球团队薪资支付的效率，确保员工按时收到报酬。
              h4 解决方案
              p SmartDeer 为客户提供了一站式全球薪资管理解决方案，助力企业在快速扩张的同时，实现高效、合规的薪资管理：
              ol
                li #[strong 全球薪资发放服务：]通过 SmartDeer 平台，支持多种货币的精准薪资计算和发放，简化国际支付流程。
                li #[strong 税务与合规支持：]实时跟踪并应用各国最新薪资和税务政策，协助企业确保合规运营。
                li #[strong 汇率锁定服务：]提供强大的锁汇功能，帮助企业应对汇率波动，降低支付成本。
                li #[strong 自动化薪资管理：]通过智能化薪资计算和发放工具，大幅减少 HR 团队的重复性工作，提升整体运营效率。
                li #[strong 定制化报告与分析：]提供详细的薪资报表和多维度的数据分析，助力企业高效掌控全球人力成本。
              h4 成果
              ul
                li #[strong 合规性风险归零：]薪资管理覆盖 10 多个国家，确保所有支付流程符合当地法规要求。
                li #[strong 成本节约 30%：]通过汇率优化和支付流程集中化管理，有效降低了国际薪资支付成本。
                li #[strong 效率提升 50%：]智能化薪资管理工具帮助企业显著减轻 HR 团队的工作负担。
                li #[strong 员工满意度提升：]全球团队成员均能按时收到薪资，进一步提升了员工的忠诚度和满意度。
              h4 客户评价
              p “SmartDeer 的全球薪资管理服务帮助我们实现了快速扩张，同时保障了薪资支付的合规性和高效性。他们的专业团队和先进工具是我们全球化战略的有力支持。” —— 客户 HR 负责人
            .solution-expand
              button(@click="toggle('s3')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s3") 展开详情
                span(v-else) 收起详情
                ArrowDown(v-if="!solutionExpandStatus.s3" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
      .solution-item.case4-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-figure
            figure
              img(src='~/assets/images/index/case4.png')
          .solution-content(:class="{collapsed: !solutionExpandStatus.s4}")
            .solution-title
              h3 一家知名生鲜电商平台 —— 中东市场扩展的全流程招聘与雇佣支持
            .solution-desc(:style="getSolutionDescStyle('s4')")
              h4 背景与痛点
              p 作为中国领先的生鲜电商平台，该企业在国内市场取得了显著成功，并于 2023 年将目光投向中东市场。为了在中东快速建立业务基础，企业需要组建包括采购、运营、商业分析和市场团队在内的本地化团队，并派遣部分中国员工到沙特支持初期业务。然而，企业在扩展过程中面临以下挑战：
              h4 主要挑战：
              ul
                li #[strong 主体设立与合规问题：]企业尚未在沙特设立法人主体，直接雇佣员工存在法律风险，同时需要规避沙特劳工法中的本地化比例（沙化率）要求。
                li #[strong 区域招聘需求复杂：]中东区域整体人才市场分布不均，招聘高技能的采购、运营、商业分析和市场人才存在较大挑战。
                li #[strong 跨国派遣管理：]从中国派遣员工到沙特需完成签证、工卡等复杂手续，并确保合法合规。
              h4 解决方案
              p SmartDeer 针对客户需求提供了全面的解决方案：
              ol
                li #[strong EOR 服务支持：]通过 SmartDeer 的本地实体直接雇佣员工，客户无需设立法人主体，合法合规地规避沙化率要求，快速启动沙特及中东业务。
                li #[strong 中东区域招聘服务：]利用 SmartDeer 的区域招聘网络，为客户快速招聘采购、运营、商业分析和市场等关键岗位人才，覆盖沙特及中东其他主要市场。
                li #[strong 跨国派遣管理：]为从中国派遣到沙特的员工提供签证办理、工卡申请和劳工文件准备等全流程服务，确保派遣过程高效合规。
                li #[strong 持续支持与咨询：]SmartDeer 的本地团队提供劳动合同管理、薪资发放和政策咨询等日常支持，减少企业在中东的人力资源管理压力。
              h4 成果
              ul
                li #[strong 快速团队组建：]在 2 个月内完成中东区域的关键岗位招聘，同时成功派遣 10 名中国员工到沙特工作，确保业务顺利启动。
                li #[strong 合规风险为零：]通过 SmartDeer 主体雇佣员工，规避了沙化率要求，确保所有操作符合沙特及中东其他国家的法律法规。
                li #[strong 时间与成本优化：]无需设立法人主体，节省了大量时间和资金，将资源集中用于核心业务运营。
                li #[strong 区域运营效率提升：]借助 SmartDeer 的全面支持，企业在沙特及中东市场实现了高效扩展，初期运营目标顺利达成。
              h4 客户评价
              p “SmartDeer 的支持让我们在沙特及中东市场的扩展更加顺利，他们不仅帮助我们解决了本地招聘难题，还为跨国派遣提供了全流程支持，确保所有流程合规高效。他们是我们国际化布局的可靠伙伴。” —— 客户 HR 负责人
            .solution-expand
              button(@click="toggle('s4')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s4") 展开详情
                span(v-else) 收起详情
                ArrowDown(v-if="!solutionExpandStatus.s4" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
      .solution-item.case5-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-figure
            figure
              img(src='~/assets/images/index/case5.png')
          .solution-content(:class="{collapsed: !solutionExpandStatus.s5}")
            .solution-title
              h3 一家全球领先的区块链技术平台 —— 全球 EOR 服务支持
            .solution-desc(:style="getSolutionDescStyle('s5')")
              h4 背景
              p 作为全球领先的区块链技术平台，该企业专注于构建去中心化互联网基础设施，并不断吸引全球顶尖的技术和运营人才。伴随着业务的快速扩展，企业需要在多个国家和地区雇佣员工以支持其全球运营，但面临以下挑战：
              h4 主要挑战：
              ul
                li #[strong 多国雇佣合规性：]不同国家的劳动法、税务和社会福利政策复杂多样，企业需确保全球范围内的雇佣流程完全合规。
                li #[strong 雇佣效率与成本：]业务扩张速度快，需要快速雇佣并安置全球团队，同时降低行政和管理成本。
                li #[strong 多币种薪资管理：]需要处理全球员工的多币种薪资支付，避免因汇率波动带来的财务风险。
              h4 解决方案
              p SmartDeer 为客户提供了一站式全球 EOR（雇主名义外包）服务，以支持其在多个国家快速扩展团队并确保运营合规：
              ol
                li #[strong 雇佣合规性管理：]通过 SmartDeer 的全球网络，为客户在不同国家合法雇佣员工，确保每一份劳动合同和薪资支付符合当地法规。
                li #[strong 快速员工安置：]利用 SmartDeer 的专业服务，帮助客户迅速完成新员工的入职流程，包括合同签订、社会保险注册和税务处理。
                li #[strong 多币种薪资支付：]SmartDeer 平台支持 150 多种货币的薪资发放，结合汇率锁定功能，帮助企业降低汇率波动带来的财务风险。
                li #[strong 日常 HR 支持：]通过 SmartDeer 平台整合报销数据流，自动化处理数据提交和分析，降低了手动操作的时间成本。SmartDeer 提供持续的 HR 支持服务，包括政策咨询、员工管理和合同续签等，减轻企业 HR 团队的负担。
              h4 成果
              ul
                li #[strong 快速全球扩展：]在短短 3 个月内协助客户完成多个国家的团队组建，支持其全球化战略的稳步推进。
                li #[strong 合规保障：]所有雇佣和薪资发放流程均符合各国法律法规，避免了潜在的法律和税务风险。
                li #[strong 成本优化：]通过 SmartDeer 集中化管理，企业节省了 30% 的人力资源管理成本和时间投入。
                li #[strong 员工满意度提升：]精准及时的薪资支付和全面的 HR 支持，进一步提高了员工的满意度和忠诚度。
              h4 客户评价
              p “SmartDeer 的全球 EOR 服务让我们能够专注于核心业务，无需担心各国复杂的雇佣和合规问题。他们的专业支持和高效服务是我们全球化运营的重要支柱。” —— 客户 HR 负责人
            .solution-expand
              button(@click="toggle('s5')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s5") 展开详情
                span(v-else) 收起详情
                ArrowDown(v-if="!solutionExpandStatus.s5" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")
      .solution-item.case6-bg(v-scroll-show="{ delayOffset: 180 }")
        .solution-wrapper
          .solution-figure
            figure
              img(src='~/assets/images/index/case6.png')
          .solution-content(:class="{collapsed: !solutionExpandStatus.s6}")
            .solution-title
              h3 一家知名在线教育平台 —— 香港 HRO 服务助力精细化人力资源管理
            .solution-desc(:style="getSolutionDescStyle('s6')")
              h4 背景
              p 作为中国领先的在线教育平台，该企业专注于为全球学生提供优质的教育资源。随着业务版图的扩展，企业在香港设立了运营中心，以支持国际化业务发展。然而，由于香港人力资源管理的特定要求和复杂性，企业在人力资源管理方面面临以下挑战：
              h4 主要挑战：
              ul
                li #[strong 薪资与法定福利管理：]需要处理香港员工的工资发放、法定福利（如强积金 MPF）扣缴，并确保所有流程符合法律要求。
                li #[strong 数据管理与合规性：]员工信息及薪资数据需要集中化管理，同时满足香港的隐私保护和劳动法
                li #[strong HR 效率提升：]企业希望通过技术手段优化人力资源管理流程，降低行政成本，提升管理效率。
              h4 解决方案
              p SmartDeer 针对客户在香港的需求，提供了全方位的 HRO（人力资源外包）解决方案：
              ol
                li #[strong 薪资与福利管理服务]
                  ul
                    li SmartDeer 负责员工薪资计算和发放，确保工资支付准确无误。
                    li 代扣代缴香港强积金（MPF）和其他法定福利，所有流程均符合法律要求，减少合规风险。
                li #[strong 实时政策咨询：]
                  ul
                    li 提供一体化 HR SaaS 平台，企业可以轻松管理员工信息、考勤、薪资数据和合同记录。
                    li 平台支持实时数据更新和自动化报表生成，减少人工操作的错误和时间投入。
                li #[strong 签证办理支持：]
                  ul
                    li 提供政策咨询和操作支持，帮助客户实时了解香港劳动法和最新政策变动。
                    li 协助客户完成员工入离职流程，确保所有操作合法合规。
              h4 成果
              ul
                li #[strong 管理效率提升 40%：]借助 SmartDeer 的 HR SaaS 平台，企业的薪资和数据管理流程大幅优化，HR 团队的日常工作负担显著减轻。
                li #[strong 合规无忧：]通过专业的薪资和福利扣缴服务，企业避免了任何法律风险，确保了员工满意度和忠诚度。
                li #[strong 成本节约 30%：]通过外包 HRO 服务，企业有效降低了人力资源管理的运营成本，将更多资源投入到核心业务发展中。
                li #[strong 实时数据掌控：]所有员工数据实现集中化、数字化管理，为企业决策提供了高效支持。
              h4 客户评价
              p “SmartDeer 的香港 HRO 服务和 HR SaaS 平台帮助我们显著提升了人力资源管理效率，特别是在薪资与法定福利管理方面，他们展现了极高的专业性。SmartDeer 是我们全球化扩展的重要合作伙伴。” —— 客户 HR 负责人
            .solution-expand
              button(@click="toggle('s6')" class="solution-toggle")
                span(v-if="!solutionExpandStatus.s6") 展开详情
                span(v-else) 收起详情
                ArrowDown(v-if="!solutionExpandStatus.s6" class="inline-arrow")
                ArrowUp(v-else class="inline-arrow")

mixin contactForm
  .contact-form
    client-only
      el-dialog(v-model="status.showForm" title="" :width="354")
        contact-us-form(lang="zh" @submit="submitSuccess")

mixin anchor
  .anchor
    .consultant(@click="status.showConsultantCode = !status.showConsultantCode")
      figure
        img(src="~/assets/images/index/anchor-avatar.png" )
    .consultant-code(v-show="status.showConsultantCode")
      .close(@click="status.showConsultantCode = false")
      figure
        img(src="~/assets/images/index/anchor-code.png" )

mixin botButton
  .bot-container(@click="toggleChat")
    img(src="~/assets/images/index/bot_logo_zh.png")

mixin goTop
  .go-top-container(@click="smoothScrollTo(500, 0)")
    img(src="~/assets/images/index/top_icon.png")

.index-page
  .main
    +header 
    +customer 
    +service 
    +advantage
    +lifecycle
    +process
    +solutionCase
    +contactForm
    +goTop

  site-footer(lang="zh" @contact-us="()=>{status.showForm = true}")
  +botButton
  +goTop
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElDialog, ElForm, ElFormItem, ElButton, ElInput, ElSelect, ElOption, ElMessage, ElDropdown, ElDropdownMenu, ElDropdownItem, ElCarousel, ElCarouselItem } from 'element-plus'
import phoneArea from '~/assets/utils/global-phone-area'
import langTool from '~/assets/utils/lang'
import CustomerList from '@/components/customer-list.vue'
import { getQueryString } from '~/assets/utils'
import videojs from "video.js";
import {ArrowUp, ArrowDown, ArrowRight} from '@element-plus/icons-vue';

definePageMeta({ layout: 'basic' })

// 获取运行时配置
const config = useRuntimeConfig()
const baseUrl = config.public.mobile_site_host || 'https://m.smartdeer.work'
const imageUrl = `${baseUrl}/images/tg_banner.png`
const pageUrl = `${baseUrl}/zh`

useHead({
  htmlAttrs: { lang: 'zh-CN' },
  title: 'SmartDeer - 全球招聘雇佣，海外人力资源一站式服务平台',
  meta: [
    { name: 'description', content: 'SmartDeer是领先的全球人力资源服务平台，提供专业的海外招聘、跨境雇佣、薪酬管理和合规服务。覆盖150+国家，助力中国企业全球化扩张，提供EOR、PEO等一站式HR解决方案。立即咨询，开启您的全球化之旅！' },
    { name: 'keywords', content: '全球招聘,海外雇佣,人力资源外包,EOR,全球薪酬,海外用工,国际招聘,SmartDeer,名义雇主,灵活用工' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { name: 'author', content: 'SmartDeer' },

    // Open Graph标签
    { property: 'og:title', content: 'SmartDeer - 全球招聘雇佣，海外人力资源一站式服务平台' },
    { property: 'og:description', content: 'SmartDeer是领先的全球人力资源服务平台，提供专业的海外招聘、跨境雇佣、薪酬管理和合规服务。覆盖150+国家，助力中国企业全球化扩张，提供EOR、PEO等一站式HR解决方案。' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: pageUrl },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:image', content: imageUrl },
    { property: 'og:image:width', content: '1390' },
    { property: 'og:image:height', content: '781' },
    { property: 'og:image:type', content: 'image/png' },
    { property: 'og:image:alt', content: 'SmartDeer - 全球人力资源服务平台' },
    { property: 'og:locale', content: 'zh_CN' },

    // Twitter Card标签
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'SmartDeer - 全球招聘雇佣，海外人力资源一站式服务平台' },
    { name: 'twitter:description', content: 'SmartDeer是领先的全球人力资源服务平台，提供专业的海外招聘、跨境雇佣、薪酬管理和合规服务。覆盖150+国家，助力中国企业全球化扩张，提供EOR、PEO等一站式HR解决方案。' },
    { name: 'twitter:image', content: imageUrl },
    { name: 'twitter:image:alt', content: 'SmartDeer - 全球人力资源服务平台' }
  ],
  link: [
    // Canonical URL
    { rel: 'canonical', href: pageUrl },

    // Hreflang标签
    { rel: 'alternate', hreflang: 'zh', href: `${baseUrl}/zh` },
    { rel: 'alternate', hreflang: 'en', href: `${baseUrl}/en` },
    { rel: 'alternate', hreflang: 'ja', href: `${baseUrl}/ja` },
    { rel: 'alternate', hreflang: 'x-default', href: baseUrl }
  ],
  script: [
    // 结构化数据 - WebSite + Organization
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@graph": [
          {
            "@type": "WebSite",
            "@id": `${baseUrl}/#website`,
            "url": baseUrl,
            "name": "SmartDeer",
            "description": "全球招聘雇佣，海外人力资源一站式服务平台",
            "publisher": {
              "@id": `${baseUrl}/#organization`
            },
            "potentialAction": [
              {
                "@type": "SearchAction",
                "target": {
                  "@type": "EntryPoint",
                  "urlTemplate": `${baseUrl}/search?q={search_term_string}`
                },
                "query-input": "required name=search_term_string"
              }
            ],
            "inLanguage": "zh-CN"
          },
          {
            "@type": "Organization",
            "@id": `${baseUrl}/#organization`,
            "name": "SmartDeer",
            "url": baseUrl,
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/images/logo.png`
            },
            "description": "SmartDeer是领先的全球人力资源服务平台，提供招聘、雇佣、薪酬管理等一站式服务",
            "sameAs": [
              "https://www.linkedin.com/company/smartdeer-global/",
              "https://twitter.com/smartdeer"
            ],
            "contactPoint": {
              "@type": "ContactPoint",
              "telephone": "+852-3008-5033",
              "contactType": "customer service",
              "availableLanguage": ["Chinese", "English", "Japanese"]
            }
          }
        ]
      })
    }
  ]
})

const scrollItems = []
const lifecycleItems = ref([0, 1, 2, 3, 4, 5, 6])
const count = lifecycleItems.value.length

const status = reactive({
  showForm: false,
  showConsultantCode: false
})

const form = reactive({
  name: '',
  company: '',
  service: '',
  countryCode: '+86',
  mobile: '',
  email: '',
  extra: ''
})

const serviceOptions = ref([
  { label: '全球人才招聘（Recruitment）' },
  { label: '全球名义雇主（EOR）' },
  { label: '全球灵活员工（Contractor）' },
  { label: '全球人力服务（PEO）' },
  { label: '其他' }
])

const countryCode = ref(phoneArea)

const solutionExpandStatus = ref({
  s1: false,
  s2: false,
  s3: false,
  s4: false,
  s5: false,
  s6: false
})

const defaultHeight = '312px'
const fullHeight = '2000px'

function getSolutionDescStyle(key) {
  return {
    maxHeight: solutionExpandStatus.value[key] ? fullHeight : defaultHeight,
    overflow: 'hidden',
    transition: 'max-height 0.8s ease-out'
  }
}

let beforeExpandTop = null
function toggle(key) {
  if (!solutionExpandStatus.value[key]) {
    beforeExpandTop = window.scrollY
  } else {
    smoothScrollTo(700, beforeExpandTop)
    beforeExpandTop = null
  }
  solutionExpandStatus.value[key] = !solutionExpandStatus.value[key];
}

function smoothScrollTo(duration, target) {
  const start = window.scrollY
  const startTime = performance.now()

  function scrollStep (currentTime) {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)
    const newScrollY = start + (target - start) * progress
    window.scrollTo(0, newScrollY)
    if (progress < 1) {
      requestAnimationFrame(scrollStep)
    }
  }
  requestAnimationFrame(scrollStep)
}

// 自定义指令
const vScrollShow = {
  mounted: (el, bindings) => {
    const delayOffset = bindings.value.delayOffset || 0
    scrollItems.push({ offsetTop: el.offsetTop + delayOffset, el: el })
  }
}

let timer = null
function scroll(e) {
  if (timer) return
  timer = setTimeout(() => {
    const offset = window.scrollY + window.innerHeight
    scrollItems.forEach((item, index) => {
      if (item.offsetTop < offset) {
        item.el.setAttribute('show', true)
        scrollItems.splice(index, 1)
      }
    })
    timer = null
  }, 30)
}

const cozeWebSDK = ref(null)

function toggleChat() {
  console.log(cozeWebSDK.value)
  if (cozeWebSDK.value) {
    cozeWebSDK.value.showChatBot()
  }
}

onMounted(() => {
  window.addEventListener('scroll', scroll)

  setInterval(() => {
    lifecycleItems.value = lifecycleItems.value.map((item) => {
      return (item + 1) % count
    })
  }, 2200)

  const curScroll = getQueryString('scroll')
  if (curScroll) {
    scrollTo('#' + curScroll)
  }

  const player = videojs('video-player', {
    controls: true
  });

  cozeWebSDK.value = new CozeWebSDK.WebChatClient({
    config: {
      botId: '7439335660751716386'
    },
    ui: {
      base: {
        icon: 'https://static.smartdeer.com/bot_logo.png',
        layout: 'mobile',
        zIndex: 1000
      },
      chatBot: {
        title: '顾问杰哥',
        uploadable: false,
      },
      asstBtn: {
        isNeed: false
      },
      footer: {
        isShow: true,
        expressionText: 'Powered by SmartDeer.'
      }
    }
  })
})

onBeforeUnmount(() => {
  window.removeEventListener('scroll', scroll)
})

function scrollTo(tag) {
  const ele = window.document.querySelector(tag)
  if (ele) window.scrollTo({
    top: ele.offsetTop,
    behavior: 'smooth'
  })
}

function submitSuccess() {
  status.showForm = false
  ElMessage.success('Submit success!')
}

function switchLang(lang) {
  langTool.swithLang(lang)
}

</script>

<style lang="scss" scoped>
.index-page {
  --max-width: 680px;
  min-width: 375px;

  section {
    .section-title {
      text-align: center;
      line-height: 1;
      margin-bottom: 20px;

      h2 {
        font-size: 20px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        line-height: 28px;
      }

      p {
        font-size: 10px;
        font-family: DIN-Regular, DIN;
        font-weight: 400;
        color: #999999;
        line-height: 12px;
      }
    }
  }
}

.anchor {
  position: fixed;
  right: 20px;
  bottom: 197px;
  z-index: 99;

  .consultant {
    width: 73px;
    height: 81px;
    cursor: pointer;
  }
  .consultant-code {
    width: 236px;
    height: 321px;
    position: fixed;
    right: 70px;
    bottom: 163px;

    .close {
      width: 20px;
      height: 20px;
      cursor: pointer;
      position: absolute;
      top: 27px;
      right: 35px;
      // border: 1px solid #000;
    }
  }

  figure {
    width: 100%;
    height: 100%;

    img{
      display: block;
      width: 100%;
      height: 100%;
    }
  }
}


header {
  display: block;
  position: relative;
  background-color: #FFF3E6;
  // height: 500px;
  width: 100%;
  overflow: hidden;

  .header-background {
    height: 100%;
    position: absolute;
    width: 100%;
    z-index: 0;
    top: 0;

    .decoration {
      transform: rotate(-45deg);
      transform-origin: 1000px 1000px;
      background: #FF8600;
      border-radius: 100px;
      height: 2000px;
      width: 2000px;
      top: 50%;
      margin-top: -876px;
      left: 50%;
      margin-left: 372px;
      position: absolute;
    }
  }

  .header-content {
    position: relative;
    z-index: 1;
    width: 100%;
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
    padding-bottom: 37px;

    .header-banner {
      display: flex;
      justify-content: space-between;
      margin-top: 29px;

      .header-banner-text{
        padding-top: 14px;
        .slogon {
          font-size: 26px;
          font-weight: 600;
          line-height: 37px;
          font-family: PingFangSC-Semibold, PingFang SC;
          white-space:nowrap;
        }

        h1.site-title {
          white-space:nowrap;
          font-size: 16px;
          font-weight: 600;
          color: #000000;
          line-height: 27px;
          margin-top: 10px;
          font-family: PingFangSC-Semibold, PingFang SC;
        }

        .desc {
          font-size: 12px;
          line-height: 20px;
          .desc-text {
            font-size: 12px;
            font-family: PingFangSC-Light, PingFang SC;
            font-weight: 300;
            line-height: 17px;
            margin-top: 10px;
            width: 174px;
            min-width: 174px;
          }
        }
      }

      .header-banner-image{
        img{
          width: 187px;
        }
      }
    }
  }
}

section.customer {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0px 20px;
  box-sizing: border-box;
  margin-top: 39px;
}

section.service {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0px 20px;
  box-sizing: border-box;
  margin-top: 53px;

  .service-item {
    margin-bottom: 30px;

    .figure-area {
      margin-bottom: 20px;

      figure {
        display: block;

        img, #video-player {
          width: 100%;
        }

        #video-player {
          width: 100%;
          height: 212px;
          background: transparent;

          video {
            border-radius: 20px;
            overflow: hidden;
          }
        }
      }
    }

    .service-content {
      .service-title {
        line-height: 1;
        margin-bottom: 10px;

        h3 {
          font-size: 20px;
          margin-bottom: 3px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333333;
          line-height: 28px;
        }

        p {
          font-size: 12px;
          font-family: DIN-Regular, DIN;
          font-weight: 400;
          color: #999;
          line-height: 15px;
        }
      }

      .service-desc {
        font-size: 11px;
        line-height: 19px;
        color: #666666;

        p {
          margin-bottom: 4px;
          text-indent: 1em;
          position: relative;

          &::before{
            content: '';
            width: 3px;
            height: 3px;
            background: #FF7F00;
            position: absolute;
            left: 0;
            top: 7px;
          }

          &:last-child{
            margin: 0;
          }
        }

        .service-contact-button {
          background: transparent;
          line-height: 24px;
          font-size: 12px;
          border-radius: 20px;
          padding: 0 15px;
          border: 1px #000 solid;
          margin-top: 10px;
          cursor: pointer;
          display: flex;
          align-items: center;
          color: #666666;
        }
      }
    }
  }
}

section.advantage {
  max-width: var(--max-width);
  margin: 0 auto;
  padding-left: 20px;
  box-sizing: border-box;

  .advantage-list {
    display: flex;
    // justify-content: space-between;
    // flex-wrap: wrap;
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;

    .advantage-item {
      // width: 36%;
      min-width: 40%;
      box-sizing: border-box;
      margin-right: 8px;
      background-color: #FEEFDF;
      padding: 14px 0;
      // height: 115px;
      border-radius: 7px;
      text-align: center;

      figure.advantage-icon-area {
        width: 100%;
        img {
          display: block;
          width: 26px;
          margin: 0 auto;
        }
      }

      .advantage-title {
        margin-top: 6px;
        padding: 0 8px;
        font-weight: bold;
        white-space: normal;
      }

      .advantage-content {
        display: block;
        overflow: hidden;
        padding: 0 8px;
        font-size: 11px;
        font-weight: 500;
        color: #333333;
        line-height: 14px;
        margin-top: 6px;
        white-space: normal;
        word-wrap: break-word;
        word-break:normal;
      }
    }
  }
}

section.lifecycle {
  margin-top: 48px;

  .lifecycle-list {
    overflow: hidden;

    .lifecycle-list-container {
      position: relative;
      display: flex;
      width: 100%;
      overflow: hidden;
      padding-bottom: 20px;
      position: relative;
      height: 128px;
      max-width: 680px;
      margin: 0 auto;

      .lifecycle-item {
        width: 128px;
        flex: 0 0 auto;
        transition: all .4s ease-in-out;
        position: absolute;

        // --------
        &[data-nth="0"] {
          opacity: 0;
          transform-origin: center;
          transform: scale(0.2);
          left: 105%;
          margin-left: -64px;
        }

        &[data-nth="6"] {
          opacity: 0;
          transform-origin: center;
          transform: scale(0.2);
          left: -10%;
          margin-left: -64px;
        }

        // --------
        &[data-nth="5"] {
          opacity: 0.2;
          transform-origin: center;
          transform: scale(0.5);
          left: 5%;
          margin-left: -64px;
        }

        &[data-nth="1"] {
          opacity: 0.2;
          transform-origin: center;
          transform: scale(0.5);
          left: 95%;
          margin-left: -64px;
        }

        // --------
        &[data-nth="4"] {
          opacity: 0.7;
          transform-origin: center;
          transform: scale(0.7);
          left: 25%;
          margin-left: -64px;
        }

        &[data-nth="2"] {
          opacity: 0.7;
          transform-origin: center;
          transform: scale(0.7);
          left: 75%;
          margin-left: -64px;
        }

        // ----
        &[data-nth="3"] {
          opacity: 1;
          transform-origin: center;
          transform: scale(1);
          left: 50%;
          margin-left: -64px;
        }

        figure {
          img {
            width: 100%;
          }
        }

        .title {
          text-align: center;
        }
      }
    }
  }
}

section.process {
  margin: 48px 0 74px 0;

  .process-list {
    .process-item {
      margin-top: 44px;

      &:nth-child(2n+1) {
        .process-head {
          // width: 100%;
          background-image: linear-gradient(90deg, #FFFFFF 15%, #FFF2E3 100%);
        }

      }

      &:nth-child(2n) {
        .process-head {
          // width: 100%;
          background-image: linear-gradient(270deg, #FFFFFF 15%, #FFF2E3 100%);
        }
      }

      .process-head {
        margin-bottom: 20px;

        .process-head-wrapper {
          max-width: var(--max-width);
          margin: 0 auto;
          padding: 0 20px;
          box-sizing: border-box;
          display: flex;

          figure {
            width: 80%;
            position: relative;
            top: -24px;

            img {
              width: 100%;
              filter: drop-shadow(0 1px 5px RGBA(0, 0, 0, .18))
            }
          }

          .num {
            width: 20%;
            flex: 1 1 auto;
            text-align: center;
            font-size: 24px;
            color: #333333;
            letter-spacing: 0;
            font-weight: bold;
            position: relative;
            top: 16px;
          }
        }
      }

      .process-content {
        max-width: var(--max-width);
        margin: 0 auto;
        padding: 0 20px;
        box-sizing: border-box;

        .title {
          font-size: 20px;
          color: #333333;
          font-weight: 500;
          line-height: 28px;
          margin-bottom: 10px;
          font-family: PingFangSC-Medium, PingFang SC;
        }

        .desc {
          font-size: 11px;
          letter-spacing: 0;
          line-height: 19px;
          font-family: PingFangSC-Light, PingFang SC;
          font-weight: 300;
        }
      }
    }
  }
}

.contact-form {
  .form-title {
    font-size: 24px;
    color: #484848;
    text-align: center;
    margin-bottom: 30px;
    margin-top: -30px;
    font-weight: bold;
  }

  .form-body {
    .mobile {
      line-height: 46px;
      border: 1px solid;
      border-radius: 8px;
      width: 100%;
    }
  }
}
// 服务模式模块
section.solution {
  max-width: var(--max-width);
  padding: 0;
  box-sizing: border-box;
  margin: 53px auto 0 auto;

  .section-title {
    margin-bottom: 0;
  }

  .solution-list {
    margin: 0 auto;

    .case1-bg {
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.4), rgba(239, 223, 207, 0.4));

      .solution-content.collapsed {
        &:after {
          content: '';
          position: absolute;
          bottom: 40px;
          left: 0;
          width: 100%;
          height: 50px;
          background-image: linear-gradient(-180deg,rgba(255,255,255,0) 0%,#F8F3EE 100%);
          pointer-events: none;
        }
      }
    }
    .case2-bg {
      background: linear-gradient(to bottom, rgba(209, 237, 217, 0), rgba(209, 237, 217, 0.4));
      .solution-content.collapsed {
        &:after {
          content: '';
          position: absolute;
          bottom: 40px;
          left: 0;
          width: 100%;
          height: 50px;
          background-image: linear-gradient(-180deg,rgba(255,255,255,0) 0%,#F0F8F1 100%);
          pointer-events: none;
        }
      }
    }
    .case3-bg {
      background: linear-gradient(to bottom, rgba(149, 201, 246, 0), rgba(149, 201, 246, 0.4));
      .solution-content.collapsed {
        &:after {
          content: '';
          position: absolute;
          bottom: 40px;
          left: 0;
          width: 100%;
          height: 50px;
          background-image: linear-gradient(-180deg,rgba(255,255,255,0) 0%,#DDECFB 100%);
          pointer-events: none;
        }
      }
    }
    .case4-bg {
      background: linear-gradient(to bottom, rgba(251, 235, 186, 0.04), rgba(251, 235, 186, 0.4));
      .solution-content.collapsed {
        &:after {
          content: '';
          position: absolute;
          bottom: 40px;
          left: 0;
          width: 100%;
          height: 50px;
          background-image: linear-gradient(-180deg,rgba(255,255,255,0) 0%,#FDF8E8 100%);
          pointer-events: none;
        }
      }
    }
    .case5-bg {
      background: linear-gradient(to bottom, rgba(209, 237, 217, 0), rgba(209, 237, 217, 0.4));
      .solution-content.collapsed {
        &:after {
          content: '';
          position: absolute;
          bottom: 40px;
          left: 0;
          width: 100%;
          height: 50px;
          background-image: linear-gradient(-180deg,rgba(255,255,255,0) 0%,#F1F8F2 100%);
          pointer-events: none;
        }
      }
    }
    .case6-bg {
      background: linear-gradient(to bottom, rgba(149, 201, 246, 0), rgba(149, 201, 246, 0.4));
      .solution-content.collapsed {
        &:after {
          content: '';
          position: absolute;
          bottom: 40px;
          left: 0;
          width: 100%;
          height: 50px;
          background-image: linear-gradient(-180deg,rgba(255,255,255,0) 0%,#DDECFB 100%);
          pointer-events: none;
        }
      }
    }

    .solution-item {
      padding-top: 30px;
      padding-bottom: 30px;

      .solution-wrapper {
        padding: 0 20px;
        margin: 0 auto;

        .solution-figure {
          position: relative;
          transition: all .5s;

          figure {
            img {
              width: 100%;
            }
          }
        }

        .solution-content {
          position: relative;
          opacity: 1;
          width: 100%;
          flex: 0 0 auto;
          box-sizing: border-box;
          transition: all .5s;

          .solution-title {
            color: #333333;

            h3 {
              font-size: 20px;
              font-weight: 500;
              font-family: PingFangSC-Medium, PingFang SC;
              line-height: 28px;
              color: #333333;
              margin: 20px 0 0 0;
            }

            p {
              font-size: 18px;
              font-family: DIN-Regular, DIN;
              font-weight: 400;
              color: #999;
              line-height: 26px;
              margin: 0;
            }
          }

          .solution-desc {
            font-size: 16px;
            color: #333;
            line-height: 27px;
            h4 {
              margin: 10px 0 10px 0;
              font-weight: bold;
            }
            ol {
              position: relative;
              padding-left: 20px;
              margin: 0
            }
            ol li {
              list-style: decimal;
              position: relative;
            }
            ul {
              position: relative;
              padding-left: 20px;
              margin: 0;
            }
            ul li {
              position: relative;
            }

            li>ul {
              padding-left: 20px;
            }
            li>ul li{
              list-style: circle;
            }
            p{
              position: relative;
              margin: 0;
            }
          }

          .solution-expand {
            margin-top: 20px;
            .solution-toggle {
              background: transparent;
              border: 1px #000 solid;
              padding: 0 20px;
              line-height: 32px;
              border-radius: 15px;
              cursor: pointer;
              display: flex;
              align-items: center;
              color: #2D2D2D;
            }
            .inline-arrow {
              width: 16px;
              height: 16px;
              margin-left: 5px;
            }
          }
        }
      }
    }
  }
}
.inline-arrow {
  width: 16px;
  height: 16px;
  margin-left: 5px;
}
.bot-container {
  z-index: 1000;
  cursor: pointer;
  display: flex;
  position: fixed;
  bottom: 104px;
  right: 20px;
  transition: transform 0.3s ease;
  &:hover {
    transform: scale(1.16);
  }
  img {
    width: 70px;
    height: 80px;
  }
}
.go-top-container {
  cursor: pointer;
  display: flex;
  position: fixed;
  bottom: 30px;
  right: 30px;

  img {
    width: 55px;
    height: 55px;
  }
}
</style>