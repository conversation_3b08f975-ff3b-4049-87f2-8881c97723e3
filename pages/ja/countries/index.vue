<!--
 * @Author: sx <EMAIL>
 * @Date: 2023-01-10 09:55:23
 * @LastEditors: sx <EMAIL>
 * @LastEditTime: 2023-01-13 17:04:51
 * @FilePath: \bpo-website-pc\pages\zh\contries\canada.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template lang="pug">
.countries-page
  header
    site-header(lang="ja" source="countries" @contact-us="status.showForm = true")

    .header-banner
      .header-banner-text
        h1.header-title グローバル人材採用
        p.header-desc グローバル人材採用の詳細情報をご覧ください。
        button.header-contact(@click="status.showForm = true") お問い合わせ
      .header-banner-image
        img(src="~/assets/images/countries/country-guide-header.png")
  .country-index-main
    h2 グローバル人材採用
    p.sub-desc グローバル人材採用の詳細情報をご覧ください。
    .search-bar
      input.text-search(placeholder="Search Country name" v-model="form.keyword" @keyup.enter="searchKeyword")
      button.btn-search(@click="searchKeyword") 検索
    ul.country-list
      a(v-for="(item, index) in paginator.currentPageData" :href="item.link")
        li
          .img-container
            img(:src="item.image.node.sourceUrl" width="100%")
          .link-container {{ item.countryName }}
    .paginator
      a.prev(v-if="paginator.currentPage !== 1" @click="previousPage" src="javascript:;") 前へ
      a.next(v-if="paginator.currentPage !== paginator.totalPages" @click="nextPage" src="javascript:;") 次へ

  site-footer(lang="ja" @contact-us="status.showForm = true")
  .contact-form
    client-only
      el-dialog(v-model="status.showForm" title="" :width="354")
        contact-us-form(lang="ja" @submit="submitSuccess")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
import articleList from "./article-list-ja.json";
import {ceil} from "lodash-es";

definePageMeta({ layout: 'basic' })
useSeoMeta({
  ogTitle: 'グローバル人材採用 - SmartDeer',
  ogDescription: 'グローバル人材採用の詳細情報をご覧ください。',
  ogSiteName: 'SmartDeer',
  description: 'グローバル人材採用の詳細情報をご覧ください。'
})
useHead({
  htmlAttrs: {
    lang: 'ja-JP'
  },
  title: 'グローバル人材採用 - SmartDeer'
})

const status = reactive({
  showForm: false
})

const form = reactive({
  keyword: ''
})

const paginator = reactive({
  currentPage: 1,
  pageSize: 12,
  totalPages: 0,
  allArticles: articleList,
  filteredArticles: [],
  currentPageData: []
})

function nextPage() {
  if (paginator.currentPage < paginator.totalPages) {
    paginator.currentPage++;
  }
  freshCurrentPageData();
}

function previousPage() {
  if (paginator.currentPage > 1) {
    paginator.currentPage--;
  }
  freshCurrentPageData();
}

function searchKeyword() {
  if (!form.keyword) {
    paginator.filteredArticles = paginator.allArticles;
  } else {
    const searchKeywordLower = form.keyword.toLowerCase();
    paginator.filteredArticles = paginator.allArticles.filter(item => {
      return JSON.stringify(item).toLowerCase().includes(searchKeywordLower);
    });
  }
  paginator.currentPage = 1
  freshCurrentPageData()
}

function freshCurrentPageData() {
  if (!form.keyword) {
    paginator.filteredArticles = paginator.allArticles
  }
  const start = (paginator.currentPage - 1) * paginator.pageSize
  const end = paginator.currentPage * paginator.pageSize
  paginator.currentPageData = paginator.filteredArticles.slice(start, end)
  paginator.totalPages = ceil(paginator.filteredArticles.length / paginator.pageSize)
}

function submitSuccess() {
  ElMessage.success('We have received your request and we will contact with you as soon as possible.')
  status.showForm = false
}

function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}

onMounted(() => {
  freshCurrentPageData()
})
</script>

<style lang="scss" scoped>
@import './country-guide-index.scss';
</style>