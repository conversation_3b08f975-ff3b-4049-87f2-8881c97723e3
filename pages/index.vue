<template lang="pug">
.index-page
  img.loading(src="~/assets/images/loading-min.svg")
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import langTool from '~/assets/utils/lang'
import {useSessionStorage} from "@vueuse/core";

definePageMeta({ layout: 'blank' })

// 获取运行时配置
const config = useRuntimeConfig()
const baseUrl = config.public.mobile_site_host || 'https://m.smartdeer.work'
const imageUrl = `${baseUrl}/images/tg_banner.png`

// SEO设置
useHead({
  htmlAttrs: { lang: 'zh-CN' },
  title: 'SmartDeer - 全球招聘雇佣，海外人力资源一站式服务平台',
  meta: [
    { name: 'description', content: 'SmartDeer提供全球招聘、海外雇佣、薪酬管理等一站式人力资源服务，覆盖150+国家，助力企业全球化扩张。专业的EOR、PEO、全球薪酬解决方案。' },
    { name: 'keywords', content: '全球招聘,海外雇佣,人力资源外包,EOR,全球薪酬,海外用工,国际招聘,SmartDeer' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { name: 'author', content: 'SmartDeer' },

    // Open Graph标签
    { property: 'og:title', content: 'SmartDeer - 全球招聘雇佣，海外人力资源一站式服务平台' },
    { property: 'og:description', content: 'SmartDeer提供全球招聘、海外雇佣、薪酬管理等一站式人力资源服务，覆盖150+国家，助力企业全球化扩张。' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: baseUrl },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:image', content: imageUrl },
    { property: 'og:image:width', content: '1390' },
    { property: 'og:image:height', content: '781' },
    { property: 'og:image:type', content: 'image/png' },
    { property: 'og:image:alt', content: 'SmartDeer - 全球人力资源服务平台' },
    { property: 'og:locale', content: 'zh_CN' },

    // Twitter Card标签
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'SmartDeer - 全球招聘雇佣，海外人力资源一站式服务平台' },
    { name: 'twitter:description', content: 'SmartDeer提供全球招聘、海外雇佣、薪酬管理等一站式人力资源服务，覆盖150+国家，助力企业全球化扩张。' },
    { name: 'twitter:image', content: imageUrl },
    { name: 'twitter:image:alt', content: 'SmartDeer - 全球人力资源服务平台' }
  ],
  link: [
    // Canonical URL
    { rel: 'canonical', href: baseUrl },

    // Hreflang标签
    { rel: 'alternate', hreflang: 'zh', href: `${baseUrl}/zh` },
    { rel: 'alternate', hreflang: 'en', href: `${baseUrl}/en` },
    { rel: 'alternate', hreflang: 'ja', href: `${baseUrl}/ja` },
    { rel: 'alternate', hreflang: 'x-default', href: baseUrl }
  ],
  script: [
    // 结构化数据 - WebSite + Organization
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@graph": [
          {
            "@type": "WebSite",
            "@id": `${baseUrl}/#website`,
            "url": baseUrl,
            "name": "SmartDeer",
            "description": "全球招聘雇佣，海外人力资源一站式服务平台",
            "publisher": {
              "@id": `${baseUrl}/#organization`
            },
            "potentialAction": [
              {
                "@type": "SearchAction",
                "target": {
                  "@type": "EntryPoint",
                  "urlTemplate": `${baseUrl}/search?q={search_term_string}`
                },
                "query-input": "required name=search_term_string"
              }
            ],
            "inLanguage": "zh-CN"
          },
          {
            "@type": "Organization",
            "@id": `${baseUrl}/#organization`,
            "name": "SmartDeer",
            "url": baseUrl,
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/images/logo.png`
            },
            "description": "SmartDeer是领先的全球人力资源服务平台，提供招聘、雇佣、薪酬管理等一站式服务",
            "sameAs": [
              "https://www.linkedin.com/company/smartdeer-global/",
              "https://twitter.com/smartdeer"
            ],
            "contactPoint": {
              "@type": "ContactPoint",
              "telephone": "+852-3008-5033",
              "contactType": "customer service",
              "availableLanguage": ["Chinese", "English", "Japanese"]
            }
          }
        ]
      })
    }
  ]
})

const router = useRouter()
const USER_REF_KEY = 'USER_REF'

onMounted(() => {
  const userRef = useSessionStorage(USER_REF_KEY, '')
  if (!userRef.value && document.referrer !== '') {
    userRef.value = document.referrer
  }
  langTool.indexGuide()
})
</script>

<style lang="scss" scoped>
.index-page {
  display: flex;
  min-height: 100vh;
  justify-content: center;
  align-items: center;
}

.loading {
  animation: rotate .5s steps(1) infinite;
  width: 40px;
  opacity: .3;
}


@keyframes rotate {
  0% {
    transform: rotate(0deg)
  }

  12.5% {
    transform: rotate(45deg)
  }

  25% {
    transform: rotate(90deg)
  }

  37.5% {
    transform: rotate(135deg)
  }

  50% {
    transform: rotate(180deg)
  }

  62.5% {
    transform: rotate(225deg)
  }

  75% {
    transform: rotate(270deg)
  }

  87.5% {
    transform: rotate(315deg)
  }
}
</style>