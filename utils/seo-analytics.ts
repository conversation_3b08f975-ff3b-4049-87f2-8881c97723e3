// SEO 分析和监控工具

interface SEOMetrics {
  title: string
  description: string
  keywords: string
  h1Count: number
  h2Count: number
  h3Count: number
  imageCount: number
  imagesWithoutAlt: number
  internalLinks: number
  externalLinks: number
  wordCount: number
  readingTime: number
  metaRobots: string
  canonical: string
  hreflang: string[]
  structuredData: any[]
  pageSpeed: {
    lcp?: number
    fid?: number
    cls?: number
  }
}

interface SEOIssue {
  type: 'error' | 'warning' | 'info'
  category: 'meta' | 'content' | 'images' | 'links' | 'performance' | 'structure'
  message: string
  element?: string
  suggestion: string
}

export class SEOAnalyzer {
  private issues: SEOIssue[] = []
  
  // 分析页面 SEO
  analyzePage(): SEOMetrics {
    const metrics: SEOMetrics = {
      title: this.getTitle(),
      description: this.getDescription(),
      keywords: this.getKeywords(),
      h1Count: this.getHeadingCount('h1'),
      h2Count: this.getHeadingCount('h2'),
      h3Count: this.getHeadingCount('h3'),
      imageCount: this.getImageCount(),
      imagesWithoutAlt: this.getImagesWithoutAlt(),
      internalLinks: this.getInternalLinksCount(),
      externalLinks: this.getExternalLinksCount(),
      wordCount: this.getWordCount(),
      readingTime: this.getReadingTime(),
      metaRobots: this.getMetaRobots(),
      canonical: this.getCanonical(),
      hreflang: this.getHreflang(),
      structuredData: this.getStructuredData(),
      pageSpeed: this.getPageSpeedMetrics()
    }
    
    this.validateSEO(metrics)
    return metrics
  }
  
  // 获取页面标题
  private getTitle(): string {
    return document.title || ''
  }
  
  // 获取页面描述
  private getDescription(): string {
    const meta = document.querySelector('meta[name="description"]') as HTMLMetaElement
    return meta?.content || ''
  }
  
  // 获取关键词
  private getKeywords(): string {
    const meta = document.querySelector('meta[name="keywords"]') as HTMLMetaElement
    return meta?.content || ''
  }
  
  // 获取标题数量
  private getHeadingCount(tag: string): number {
    return document.querySelectorAll(tag).length
  }
  
  // 获取图片数量
  private getImageCount(): number {
    return document.querySelectorAll('img').length
  }
  
  // 获取没有 alt 属性的图片数量
  private getImagesWithoutAlt(): number {
    return document.querySelectorAll('img:not([alt]), img[alt=""]').length
  }
  
  // 获取内部链接数量
  private getInternalLinksCount(): number {
    const links = document.querySelectorAll('a[href]')
    let count = 0
    links.forEach(link => {
      const href = (link as HTMLAnchorElement).href
      if (href.startsWith(window.location.origin) || href.startsWith('/')) {
        count++
      }
    })
    return count
  }
  
  // 获取外部链接数量
  private getExternalLinksCount(): number {
    const links = document.querySelectorAll('a[href]')
    let count = 0
    links.forEach(link => {
      const href = (link as HTMLAnchorElement).href
      if (href.startsWith('http') && !href.startsWith(window.location.origin)) {
        count++
      }
    })
    return count
  }
  
  // 获取字数
  private getWordCount(): number {
    const text = document.body.innerText || ''
    return text.trim().split(/\s+/).length
  }
  
  // 获取阅读时间（分钟）
  private getReadingTime(): number {
    const wordsPerMinute = 200
    return Math.ceil(this.getWordCount() / wordsPerMinute)
  }
  
  // 获取 robots meta
  private getMetaRobots(): string {
    const meta = document.querySelector('meta[name="robots"]') as HTMLMetaElement
    return meta?.content || ''
  }
  
  // 获取 canonical URL
  private getCanonical(): string {
    const link = document.querySelector('link[rel="canonical"]') as HTMLLinkElement
    return link?.href || ''
  }
  
  // 获取 hreflang 链接
  private getHreflang(): string[] {
    const links = document.querySelectorAll('link[rel="alternate"][hreflang]')
    return Array.from(links).map(link => (link as HTMLLinkElement).hreflang)
  }
  
  // 获取结构化数据
  private getStructuredData(): any[] {
    const scripts = document.querySelectorAll('script[type="application/ld+json"]')
    const data: any[] = []
    
    scripts.forEach(script => {
      try {
        const json = JSON.parse(script.textContent || '')
        data.push(json)
      } catch (e) {
        console.warn('Invalid JSON-LD:', e)
      }
    })
    
    return data
  }
  
  // 获取页面速度指标
  private getPageSpeedMetrics(): SEOMetrics['pageSpeed'] {
    const metrics: SEOMetrics['pageSpeed'] = {}
    
    if ('PerformanceObserver' in window) {
      // 这些值通常在页面加载后异步获取
      // 这里返回已收集的值或默认值
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigation) {
        metrics.lcp = navigation.loadEventEnd - navigation.loadEventStart
      }
    }
    
    return metrics
  }
  
  // 验证 SEO 问题
  private validateSEO(metrics: SEOMetrics): void {
    this.issues = []
    
    // 标题检查
    if (!metrics.title) {
      this.addIssue('error', 'meta', '页面缺少标题', '<title>', '添加描述性的页面标题')
    } else if (metrics.title.length < 30) {
      this.addIssue('warning', 'meta', '页面标题过短', '<title>', '标题应该在30-60个字符之间')
    } else if (metrics.title.length > 60) {
      this.addIssue('warning', 'meta', '页面标题过长', '<title>', '标题应该在30-60个字符之间')
    }
    
    // 描述检查
    if (!metrics.description) {
      this.addIssue('error', 'meta', '页面缺少描述', 'meta[name="description"]', '添加150-160字符的页面描述')
    } else if (metrics.description.length < 120) {
      this.addIssue('warning', 'meta', '页面描述过短', 'meta[name="description"]', '描述应该在120-160个字符之间')
    } else if (metrics.description.length > 160) {
      this.addIssue('warning', 'meta', '页面描述过长', 'meta[name="description"]', '描述应该在120-160个字符之间')
    }
    
    // H1 标签检查
    if (metrics.h1Count === 0) {
      this.addIssue('error', 'structure', '页面缺少 H1 标签', 'h1', '添加一个描述性的 H1 标签')
    } else if (metrics.h1Count > 1) {
      this.addIssue('warning', 'structure', '页面有多个 H1 标签', 'h1', '每个页面应该只有一个 H1 标签')
    }
    
    // 图片 alt 检查
    if (metrics.imagesWithoutAlt > 0) {
      this.addIssue('warning', 'images', `${metrics.imagesWithoutAlt} 张图片缺少 alt 属性`, 'img', '为所有图片添加描述性的 alt 属性')
    }
    
    // 内容长度检查
    if (metrics.wordCount < 300) {
      this.addIssue('warning', 'content', '页面内容过少', 'body', '增加更多有价值的内容，建议至少300字')
    }
    
    // Canonical URL 检查
    if (!metrics.canonical) {
      this.addIssue('warning', 'meta', '页面缺少 canonical URL', 'link[rel="canonical"]', '添加 canonical URL 避免重复内容问题')
    }
  }
  
  // 添加问题
  private addIssue(type: SEOIssue['type'], category: SEOIssue['category'], message: string, element: string, suggestion: string): void {
    this.issues.push({
      type,
      category,
      message,
      element,
      suggestion
    })
  }
  
  // 获取问题列表
  getIssues(): SEOIssue[] {
    return this.issues
  }
  
  // 获取 SEO 评分
  getSEOScore(): number {
    const totalChecks = 10
    const errors = this.issues.filter(issue => issue.type === 'error').length
    const warnings = this.issues.filter(issue => issue.type === 'warning').length
    
    const score = Math.max(0, 100 - (errors * 15) - (warnings * 5))
    return Math.round(score)
  }
  
  // 生成 SEO 报告
  generateReport(): string {
    const metrics = this.analyzePage()
    const score = this.getSEOScore()
    
    let report = `SEO 分析报告\n`
    report += `=================\n\n`
    report += `总体评分: ${score}/100\n\n`
    
    report += `基础信息:\n`
    report += `- 标题: ${metrics.title}\n`
    report += `- 描述: ${metrics.description}\n`
    report += `- 字数: ${metrics.wordCount}\n`
    report += `- 阅读时间: ${metrics.readingTime} 分钟\n\n`
    
    report += `结构分析:\n`
    report += `- H1 标签: ${metrics.h1Count}\n`
    report += `- H2 标签: ${metrics.h2Count}\n`
    report += `- H3 标签: ${metrics.h3Count}\n`
    report += `- 图片总数: ${metrics.imageCount}\n`
    report += `- 缺少 alt 的图片: ${metrics.imagesWithoutAlt}\n`
    report += `- 内部链接: ${metrics.internalLinks}\n`
    report += `- 外部链接: ${metrics.externalLinks}\n\n`
    
    if (this.issues.length > 0) {
      report += `发现的问题:\n`
      this.issues.forEach((issue, index) => {
        report += `${index + 1}. [${issue.type.toUpperCase()}] ${issue.message}\n`
        report += `   建议: ${issue.suggestion}\n\n`
      })
    }
    
    return report
  }
}

// 导出分析器实例
export const seoAnalyzer = new SEOAnalyzer()
