<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在黑山雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在黑山雇佣',
  ogDescription: '基础信息 首都：波德戈里察 时区：GMT+1 语言：黑山语 货币代码：EUR 人力资源制度概况 雇佣合同 合同 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：波德戈里察 时区：GMT+1 语言：黑山语 货币代码：EUR 人力资源制度概况 雇佣合同 合同 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在黑山雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在黑山雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/faa42757175214ef4c5f7cb9266e2fcb.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/R-C-5.jpeg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：波德戈里察</p><p>时区：GMT+1</p><p>语言：黑山语</p><p>货币代码：EUR</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading has-large-font-size">雇佣合同</h3><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">合同必须用黑山语书写，也可以是双语的。合同必须书面并由双方湿墨签名。</span></p><p>合同应包括以下细节：</p><ul><li>全名</li><li>开始日期</li><li>就业期限</li><li>工作描述</li><li>终止条件</li></ul><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">最短试用期为30天，最长试用期为180天。</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;EoRAd840BoY28exLv14cAQlYnge&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;最短试用期为30天，最长试用期为180天。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+l&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:122,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:21},&quot;recordId&quot;:&quot;ZaaCdRXjGo4bs2xr72ZcZNCcnhe&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">标准工作时间为每天8小时，每周40小时。标准工作周从星期一到星期五。</span></p><p>加班费是强制性的。员工每年最多可以工作250小时的加班。对于额外的小时，员工将获得：</p><ul><li>标准工时时薪的140%。</li><li>周末时薪的180%。</li><li>法定假日时薪的180%</li></ul><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">黑山的全职雇员最低工资为每月450欧元。兼职雇员的最低工资为每月225欧元。</span></p><p>在黑山，工人的年薪递增是由其服务年限确定的。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p><span style="font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">  雇主成本通常估计为员工工资的8.5%。</span></p><ul><li>养老保险 &#8211; 5.50%</li><li>失业保险 &#8211; 0.50%</li><li>劳工基金 &#8211; 0.20%</li><li>商会 &#8211; 0.27%</li><li>市政当局附加税 15%*</li></ul><p>*市政当局附加税是雇主成本，必须根据雇员的税收支付。由于它是一种附加税，因此它将是税收的一部分，而不是总工资的一部分。</p><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading has-large-font-size">终止原因</h3><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">在黑山终止雇佣关系可能是一个复杂的过程。终止文件必须在员工和公司代表的面前公证。</span></p><p>合规的终止可能通过以下方式进行：</p><ul><li>员工自愿辞职</li><li>合同双方的相互协议</li><li>雇主单方面根据以下情况终止：<ul><li>试用期评估</li><li>客观原因</li><li>纪律原因</li><li>不适合工作表现</li><li>合同到期</li></ul></li></ul><h2 class="wp-block-heading has-large-font-size">通知期</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">最短通知期为30天，取决于劳动法第177条规定的条件。</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;EoRAd840BoY28exLv14cAQlYnge&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;最短通知期为30天，取决于劳动法第177条规定的条件。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+r&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:104,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:27},&quot;recordId&quot;:&quot;MmyBdbJlLoc2l8xZBjjcpYF3nFg&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">在黑山，无故解雇的员工有权获得解雇补偿。解雇补偿金额应为员工在雇主处服务每年的平均月工资的至少三分之一。</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;EoRAd840BoY28exLv14cAQlYnge&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;在黑山，无故解雇的员工有权获得解雇补偿。解雇补偿金额应为员工在雇主处服务每年的平均月工资的至少三分之一。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+1g&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:106,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:52},&quot;recordId&quot;:&quot;Jgd3d8ec5o6G38xPZ8gc3XWvn7f&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading has-large-font-size">薪资支付</h3><p>每月一次</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>无明确规定</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职员工每年有权享受20个工作日的带薪休假（PTO）。带薪休假每月累积1.66天。</p><p>如果员工每周工作6天，则最低休假时间为24个工作日。员工每年必须连续休假至少10个工作日。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">员工有权享受长达365天的带薪病假。这种假期的费率和支付人根据其持续时间而变化：</span></p><ul><li>在第60天之前，员工将获得雇主支付的工资的70%。</li><li>从第60天到第360天，员工将获得社会保障支付的工资的70%。</li></ul><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>育婴假从产假结束后开始，持续至距离分娩后365天到期。父母双方可以平等分担育婴假，但不能同时使用。如果一方父母开始使用育婴假，另一方父母可以在离开开始的30天后继续使用。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading has-large-font-size">个人所得税</h3><p><span style="font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">&nbsp;&nbsp;个人所得税的税率最高为15%。个人所得税根据个人的收入计算。</span></p><figure class="wp-block-table"><table><tbody><tr><td>每月收入不超过700欧元</td><td>0%</td></tr><tr><td>每月收入从701欧元到1000欧元</td><td>9%</td></tr><tr><td>超过1001欧元</td><td>15%</td></tr></tbody></table></figure><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">地方附加税是个人所得税（PIT）旁边附加征收的税金，必须支付给纳税人居住的市政当局。所有市政当局均适用13%的附加税率，但Podgorica和Cetinje的税率为15%。</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;EoRAd840BoY28exLv14cAQlYnge&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;地方附加税是个人所得税（PIT）旁边附加征收的税金，必须支付给纳税人居住的市政当局。所有市政当局均适用13%的附加税率，但Podgorica和Cetinje的税率为15%。&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+2e&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:84,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:86},&quot;recordId&quot;:&quot;ZCD8dLOu2o9Gn8xepOAcW1UCnGf&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">社会保险</h2><ul><li>养老保险</li><li>公共医疗保险</li><li>社会保障</li><li>失业保险</li></ul><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>日期</td></tr><tr><td>New Year&#8217;s Day&nbsp;&nbsp;&nbsp;&nbsp;</td><td>1.1-1.2</td></tr><tr><td>Orthodox Christmas Eve</td><td>1.6-1.8</td></tr><tr><td>Labour Day</td><td>5.1-5.3</td></tr><tr><td>Orthodox Easter Monday</td><td>5.6</td></tr><tr><td>Independence Day</td><td>5.21-5.22</td></tr><tr><td>National Day</td><td>7.13</td></tr><tr><td>National Day Holiday</td><td>7.15</td></tr><tr><td>Njegos Day</td><td>11.13-11.14</td></tr></tbody></table></figure>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>