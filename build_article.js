const fs = require('fs');
const {GraphQLClient} = require("graphql-request");

const endpoint = 'https://blog.smartdeer.work/graphql';

const articleGuideListZh = [];
const articleGuideListEn = [];
const articleGuideListJa = [];
const articleListZh = [];
const articleListEn = [];
const articleListJa = [];
const articleMarketingListZh = [];
const articleMarketingListEn = [];
const articleMarketingListJa = [];

const articleLegalListZh = [];
const articleLegalListEn = [];
const articleLegalListJa = [];

let hasNextPage = true;
let afterArticleId = '';

function getArticleFromServer() {
    const query = buildGraphql({
        orderby: {
            field: 'DATE',
            order: 'DESC'
        },
    }, afterArticleId, 10);
    const client = new GraphQLClient(endpoint, {});
    client.request(query).then(data => {
        // console.log(data)
        const posts = data.posts.nodes;
        // 生成 Country Guide Detail 页
        posts.forEach(post => {
            generateArticleDetail(post);
        })
        hasNextPage = data.posts.pageInfo.hasNextPage;
        afterArticleId = data.posts.pageInfo.endCursor;
        if (hasNextPage) {
            getArticleFromServer();
        } else {
            generateArticleList();
        }
        // 生成 Country Guide 首页
    }).catch(error => {
        console.log("Fetch Error: ", error);
        hasNextPage = false;
        afterArticleId = '';
    });
}

function generateArticleDetail(post) {
    if (post.content) {
        post.content = post.content.replace(/\n/g, '');
        post.content = post.content.replace(/'/g, '\\\'');
    }
    const vueTemplate = fs.readFileSync('./article-detail-template.vue');
    const vueFile = `${vueTemplate}`;

    const vueFlagTemplate = fs.readFileSync('./article-detail-flag-template.vue');
    const vueFlagFile = `${vueFlagTemplate}`;

    const vueNoBannerTemplate = fs.readFileSync('./article-detail-nobanner-template.vue')
    const vueNoBannerFile = `${vueNoBannerTemplate}`;

    const vueNoBannerNoflagNocontactTemplate = fs.readFileSync('./article-detail-nobanner-noflag-nocontact-template.vue')
    const vueNoBannerNoflagNocontactFile = `${vueNoBannerNoflagNocontactTemplate}`;

    const articleListItemData = {
        id: post.id,
        title: post.title,
        image: post.countryGuideExternal.listingImage,
        countryName: post.countryGuideExternal.countryName
    }
    // console.log(`articleListItemData: `, articleListItemData);
    // console.log(`post: `, post.categories.nodes);
    post.categories.nodes.forEach(item => {
        console.log(`vueFileContentEn1111111111: `, item)
        let vueFileContentZh = vueFile.replaceAll('${TITLE}', post.title);
        let vueFileContentEn = vueFile.replaceAll('${TITLE}', post.title);
        let vueFileContentJa = vueFile.replaceAll('${TITLE}', post.title);
        if (item.categoryId === 4 || item.categoryId === 5) {
            vueFileContentZh = vueFlagFile.replaceAll('${TITLE}', post.title);
            vueFileContentEn = vueFlagFile.replaceAll('${TITLE}', post.title);
            vueFileContentJa = vueFlagFile.replaceAll('${TITLE}', post.title);
        }
        if (item.categoryId === 7 || item.categoryId === 8) {
            vueFileContentZh = vueNoBannerFile.replaceAll('${TITLE}', post.title);
            vueFileContentEn = vueNoBannerFile.replaceAll('${TITLE}', post.title);
            vueFileContentJa = vueNoBannerFile.replaceAll('${TITLE}', post.title);
        }
        vueFileContentZh = vueFileContentZh.replaceAll('${DESCRIPTION}', post.seo.opengraphDescription);
        vueFileContentEn = vueFileContentEn.replaceAll('${DESCRIPTION}', post.seo.opengraphDescription);
        vueFileContentJa = vueFileContentJa.replaceAll('${DESCRIPTION}', post.seo.opengraphDescription);
        vueFileContentZh = vueFileContentZh.replaceAll('${SITE_NAME}', post.seo.opengraphSiteName);
        vueFileContentEn = vueFileContentEn.replaceAll('${SITE_NAME}', post.seo.opengraphSiteName);
        vueFileContentJa = vueFileContentJa.replaceAll('${SITE_NAME}', post.seo.opengraphSiteName);
        vueFileContentZh = vueFileContentZh.replaceAll('${FEATURE_IMAGE_URL}', post.featuredImage ? post.featuredImage.node.sourceUrl : '');
        vueFileContentEn = vueFileContentEn.replaceAll('${FEATURE_IMAGE_URL}', post.featuredImage ? post.featuredImage.node.sourceUrl : '');
        vueFileContentJa = vueFileContentJa.replaceAll('${FEATURE_IMAGE_URL}', post.featuredImage ? post.featuredImage.node.sourceUrl : '');
        vueFileContentZh = vueFileContentZh.replaceAll('${COUNTRY_FLAT_IMAGE_URL}', post.countryGuideExternal.countryFlagImage ? post.countryGuideExternal.countryFlagImage.node.sourceUrl: '');
        vueFileContentZh = vueFileContentZh.replaceAll('${CONTENT}', post.content);
        vueFileContentEn = vueFileContentEn.replaceAll('${CONTENT}', post.content);
        vueFileContentJa = vueFileContentJa.replaceAll('${CONTENT}', post.content);

        if (item.categoryId === 4) {
            vueFileContentZh = vueFileContentZh.replaceAll('${LANGUAGE}', 'zh-CN');
            vueFileContentZh = vueFileContentZh.replaceAll('${LANGUAGE_SIMPLE}', 'zh');
            vueFileContentZh = vueFileContentZh.replaceAll('${FORM_CONFIRM_PROMPT}', '您的请求已收到，我们会尽快与您联系。');
            fs.writeFileSync(`./pages/zh/countries/${post.countryGuideExternal.fileName}.vue`, vueFileContentZh);
            articleGuideListZh.push({
              ...articleListItemData,
              link: `/zh/countries/${post.countryGuideExternal.fileName}`
            });
        }
        if (item.categoryId === 5) {
            vueFileContentEn = vueFileContentEn.replaceAll('${LANGUAGE}', 'en-US');
            vueFileContentEn = vueFileContentEn.replaceAll('${LANGUAGE_SIMPLE}', 'en');
            vueFileContentEn = vueFileContentEn.replaceAll('${FORM_CONFIRM_PROMPT}', 'We have received your request and we will contact with you as soon as possible.');
            fs.writeFileSync(`./pages/en/countries/${post.countryGuideExternal.fileName}.vue`, vueFileContentEn);
            articleGuideListEn.push({
              ...articleListItemData,
              link: `/en/countries/${post.countryGuideExternal.fileName}`
            });
 
            // 生成日本语页面
            vueFileContentJa = vueFileContentJa.replaceAll('${LANGUAGE}', 'ja-JP');
            vueFileContentJa = vueFileContentJa.replaceAll('${LANGUAGE_SIMPLE}', 'ja');
            vueFileContentJa = vueFileContentJa.replaceAll('${FORM_CONFIRM_PROMPT}', 'お問い合わせを受け付けました。できるだけ早くご連絡いたします。');
            fs.writeFileSync(`./pages/ja/countries/${post.countryGuideExternal.fileName}.vue`, vueFileContentJa);
            articleGuideListJa.push({
              ...articleListItemData,
              link: `/ja/countries/${post.countryGuideExternal.fileName}`
            });
        }
        if (item.categoryId === 8) {
            vueFileContentZh = vueFileContentZh.replaceAll('${LANGUAGE}', 'zh-CN');
            vueFileContentZh = vueFileContentZh.replaceAll('${LANGUAGE_SIMPLE}', 'zh');
            vueFileContentZh = vueFileContentZh.replaceAll('${FORM_CONFIRM_PROMPT}', '您的请求已收到，我们会尽快与您联系。');
            fs.writeFileSync(`./pages/zh/articles/${post.countryGuideExternal.fileName}.vue`, vueFileContentZh);
            articleListZh.push({
              ...articleListItemData,
              link: `/zh/articles/${post.countryGuideExternal.fileName}`
            });
        }
        if (item.categoryId === 7) {
            vueFileContentEn = vueFileContentEn.replaceAll('${LANGUAGE}', 'en-US');
            vueFileContentEn = vueFileContentEn.replaceAll('${LANGUAGE_SIMPLE}', 'en');
            vueFileContentEn = vueFileContentEn.replaceAll('${FORM_CONFIRM_PROMPT}', 'We have received your request and we will contact with you as soon as possible.');
            fs.writeFileSync(`./pages/en/articles/${post.countryGuideExternal.fileName}.vue`, vueFileContentEn);
            articleListEn.push({
              ...articleListItemData,
              link: `/en/articles/${post.countryGuideExternal.fileName}`
            });

            // 生成日本语页面
            vueFileContentJa = vueFileContentJa.replaceAll('${LANGUAGE}', 'ja-JP');
            vueFileContentJa = vueFileContentJa.replaceAll('${LANGUAGE_SIMPLE}', 'ja');
            vueFileContentJa = vueFileContentJa.replaceAll('${FORM_CONFIRM_PROMPT}', 'お問い合わせを受け付けました。できるだけ早くご連絡いたします。');
            fs.writeFileSync(`./pages/ja/articles/${post.countryGuideExternal.fileName}.vue`, vueFileContentJa);
            articleListJa.push({
              ...articleListItemData,
              link: `/ja/articles/${post.countryGuideExternal.fileName}`
            });
        }
        if (item.categoryId === 11) {
            vueFileContentZh = vueFileContentZh.replaceAll('${LANGUAGE}', 'zh-CN');
            vueFileContentZh = vueFileContentZh.replaceAll('${LANGUAGE_SIMPLE}', 'zh');
            vueFileContentZh = vueFileContentZh.replaceAll('${FORM_CONFIRM_PROMPT}', '您的请求已收到，我们会尽快与您联系。');
            fs.writeFileSync(`./pages/zh/marketing/${post.countryGuideExternal.fileName}.vue`, vueFileContentZh);
            articleMarketingListZh.push({
              ...articleListItemData,
              link: `/zh/marketing/${post.countryGuideExternal.fileName}`
            });
        }
        if (item.categoryId === 10) {
            vueFileContentEn = vueFileContentEn.replaceAll('${LANGUAGE}', 'en-US');
            vueFileContentEn = vueFileContentEn.replaceAll('${LANGUAGE_SIMPLE}', 'en');
            vueFileContentEn = vueFileContentEn.replaceAll('${FORM_CONFIRM_PROMPT}', 'We have received your request and we will contact with you as soon as possible.');
            fs.writeFileSync(`./pages/en/marketing/${post.countryGuideExternal.fileName}.vue`, vueFileContentEn);
            articleMarketingListEn.push({
              ...articleListItemData,
              link: `/en/marketing/${post.countryGuideExternal.fileName}`
            });

            // 生成日本语页面
            vueFileContentJa = vueFileContentJa.replaceAll('${LANGUAGE}', 'ja-JP');
            vueFileContentJa = vueFileContentJa.replaceAll('${LANGUAGE_SIMPLE}', 'ja');
            vueFileContentJa = vueFileContentJa.replaceAll('${FORM_CONFIRM_PROMPT}', 'お問い合わせを受け付けました。できるだけ早くご連絡いたします。');
            fs.writeFileSync(`./pages/ja/marketing/${post.countryGuideExternal.fileName}.vue`, vueFileContentJa);
            articleMarketingListJa.push({
              ...articleListItemData,
              link: `/ja/marketing/${post.countryGuideExternal.fileName}`
            });
        }

        if (item.categoryId === 13) {
          console.log(`articleListItemData: `, articleListItemData);
          // 从模板开始，逐步替换所有变量
          vueFileContentZh = vueNoBannerNoflagNocontactFile.replaceAll('${LANGUAGE}', 'zh-CN');
          vueFileContentZh = vueFileContentZh.replaceAll('${LANGUAGE_SIMPLE}', 'zh');
          vueFileContentZh = vueFileContentZh.replaceAll('${TITLE}', articleListItemData.title);
          vueFileContentZh = vueFileContentZh.replaceAll('${CONTENT}', post.content);
          // 替换 SEO 相关变量
          vueFileContentZh = vueFileContentZh.replaceAll('${DESCRIPTION}', post.seo?.metaDesc || articleListItemData.title);
          vueFileContentZh = vueFileContentZh.replaceAll('${SITE_NAME}', 'SmartDeer');
          vueFileContentZh = vueFileContentZh.replaceAll('${FEATURE_IMAGE_URL}', post.countryGuideExternal?.listingImage || '');
          vueFileContentZh = vueFileContentZh.replaceAll('${COUNTRY_FLAT_IMAGE_URL}', '');
          vueFileContentZh = vueFileContentZh.replaceAll('${FORM_CONFIRM_PROMPT}', '您的请求已收到，我们会尽快与您联系。');
          // 中文
          fs.writeFileSync(`./pages/zh/legal/${post.countryGuideExternal.fileName}.vue`, vueFileContentZh);
          articleLegalListZh.push({
            ...articleListItemData,
            link: `/zh/legal/${post.countryGuideExternal.fileName}`
          });
        }

        if (item.categoryId === 14) {
          console.log(`articleListItemData: `, articleListItemData);
          // 从模板开始，逐步替换所有变量
          vueFileContentEn = vueNoBannerNoflagNocontactFile.replaceAll('${LANGUAGE}', 'en-US');
          vueFileContentEn = vueFileContentEn.replaceAll('${LANGUAGE_SIMPLE}', 'en');
          vueFileContentEn = vueFileContentEn.replaceAll('${TITLE}', articleListItemData.title);
          vueFileContentEn = vueFileContentEn.replaceAll('${CONTENT}', post.content);
          // 替换 SEO 相关变量
          vueFileContentEn = vueFileContentEn.replaceAll('${DESCRIPTION}', post.seo?.metaDesc || articleListItemData.title);
          vueFileContentEn = vueFileContentEn.replaceAll('${SITE_NAME}', 'SmartDeer');
          vueFileContentEn = vueFileContentEn.replaceAll('${FEATURE_IMAGE_URL}', post.countryGuideExternal?.listingImage || '');
          vueFileContentEn = vueFileContentEn.replaceAll('${COUNTRY_FLAT_IMAGE_URL}', '');
          vueFileContentEn = vueFileContentEn.replaceAll('${FORM_CONFIRM_PROMPT}', 'We have received your request and we will contact with you as soon as possible.');
          // 英文
          fs.writeFileSync(`./pages/en/legal/${post.countryGuideExternal.fileName}.vue`, vueFileContentEn);
          articleLegalListEn.push({
            ...articleListItemData,
            link: `/en/legal/${post.countryGuideExternal.fileName}`
          });
        }
    });
}

function generateArticleList() {
    const articleGuideZhString = JSON.stringify(articleGuideListZh);
    const articleGuideEnString = JSON.stringify(articleGuideListEn);
    const articleGuideJaString = JSON.stringify(articleGuideListJa);
    const articleListZhString = JSON.stringify(articleListZh);
    const articleListEnString = JSON.stringify(articleListEn);
    const articleListJaString = JSON.stringify(articleListJa);
    const articleMarketingListZhString = JSON.stringify(articleMarketingListZh);
    const articleMarketingListEnString = JSON.stringify(articleMarketingListEn);
    const articleMarketingListJaString = JSON.stringify(articleMarketingListJa);

    const articleLegalListZhString = JSON.stringify(articleLegalListZh);
    const articleLegalListEnString = JSON.stringify(articleLegalListEn);
    // const articleLegalListJaString = JSON.stringify(articleMarketingListJa);
    // console.log(articleGuideZhString);
    // console.log(articleGuideEnString);
    // console.log(articleGuideJaString);
    // console.log(articleListZhString);
    // console.log(articleListEnString);
    // console.log(articleListJaString);
    // console.log(articleMarketingListZhString);
    // console.log(articleMarketingListEnString);
    // console.log(articleMarketingListJaString);
    fs.writeFileSync('./pages/zh/countries/article-list-zh.json', articleGuideZhString);
    fs.writeFileSync('./pages/en/countries/article-list-en.json', articleGuideEnString);
    fs.writeFileSync('./pages/ja/countries/article-list-ja.json', articleGuideJaString);
    fs.writeFileSync('./pages/zh/articles/article-list-zh.json', articleListZhString);
    fs.writeFileSync('./pages/en/articles/article-list-en.json', articleListEnString);
    fs.writeFileSync('./pages/ja/articles/article-list-ja.json', articleListJaString);
    fs.writeFileSync('./pages/zh/marketing/article-list-zh.json', articleMarketingListZhString);
    fs.writeFileSync('./pages/en/marketing/article-list-en.json', articleMarketingListEnString);
    fs.writeFileSync('./pages/ja/marketing/article-list-ja.json', articleMarketingListJaString);

    fs.writeFileSync('./pages/zh/legal/article-list-zh.json', articleLegalListZhString);
    fs.writeFileSync('./pages/en/legal/article-list-en.json', articleLegalListEnString);
}

function buildGraphql (where, after, limit) {
    let condition = 'first: ' + limit;
    condition += ", ";
    if (after) {
        condition += 'after: "' + after + '"';
        condition += ", ";
    }
    let whereJsonString = JSON.stringify(where);
    condition += "where: " + whereJsonString.replace(/"/g, '');
    return `
query articleQuery {
  posts(${condition}) {
    nodes {
      id
      title
      slug
      categories {
        nodes {
          categoryId
        }
      }
      seo {
        metaDesc
        metaKeywords
        opengraphDescription
        opengraphSiteName
        opengraphType
        title
      }
      featuredImage {
        node {
          sourceUrl(size: LARGE)
        }
      }
      countryGuideExternal {
        listingImage {
          node {
            sourceUrl(size: LARGE)
          }
        }
        countryFlagImage {
          node {
            sourceUrl(size: LARGE)
          }
        }
        countryName
        fileName
      }
      content
    }
    pageInfo {
      startCursor
      hasPreviousPage
      hasNextPage
      endCursor
    }
  }
}
`;
}

getArticleFromServer();