<template lang="pug">
.contries-page
  site-header(lang="ja" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title Employer of Record in Croatia
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="ja" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="ja" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: 'Employer of Record in Croatia',
  ogDescription: 'Basic information Capital:Zagreb Time zone: UTC +2 Lang […]',
  ogSiteName: 'SmartDeer',
  description: 'Basic information Capital:Zagreb Time zone: UTC +2 Lang […]'
})
useHead({
  htmlAttrs: {
    lang: 'ja-JP'
  },
  title: 'Employer of Record in Croatia'
})
const status = reactive({
  showForm: false
})
const langSimple = 'ja';
const pageTitle = 'Employer of Record in Croatia';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/01e7b8592d3517b5b3086ed4db5915-1.jpg@1280w_1l_2o_100sh-1.jpg';
const flagImage = '${COUNTRY_FLAT_IMAGE_URL}';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">Basic information</h2><p>Capital:Zagreb</p><p>Time zone: UTC +2</p><p>Languages: Croatian</p><p>Currency code: EUR</p><h2 class="wp-block-heading has-large-font-size">Human Resources at a Glance&nbsp;</h2><h3 class="wp-block-heading has-large-font-size">Contract Terms</h3><p>Contracts must be in Croatian and can be bilingual. They must be in writing and signed by both parties.<br>A contract must include:</p><ul><li>Name</li><li>Position and job description</li><li>Obligations</li><li>Place of work</li><li>Commencement and duration of employment</li><li>Working hours</li><li>Salary and other benefits</li><li>Additional remuneration and reimbursement of expenses</li><li>Vacation and leave</li><li>Termination conditions</li></ul><h2 class="wp-block-heading has-large-font-size">Guidelines Regarding Probation Period/Trial Period</h2><p>Probation periods are not mandatory. There is no minimum probation period. The maximum probation period is 6 months.</p><h2 class="wp-block-heading has-large-font-size">Regulations and Rules Regarding Working Hours</h2><p>Standard working hours are 8 hours per day, 40 hours per week. The standard workweek is from Monday to Friday.</p><h2 class="wp-block-heading has-large-font-size">Overtime is paid according to the following rates</h2><p>Under Croatian Labour law, hours deemed to be overtime are subject to an additional payment, usually 1.5 times the base salary. Overtime work on public holidays is paid twice the basic wage.</p><h2 class="wp-block-heading has-large-font-size">Minimum Wage Requirements</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">From January 1, 2024, the minimum wage for full-time employees will increase to EUR 840 gross a month. The amount will be prorated for part-time employees. </span><span data-lark-record-data="{&quot;rootId&quot;:&quot;LfYZd4TeJo2PX3xn9mycaVkwnnd&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;From January 1, 2024, the minimum wage for full-time employees will increase to EUR 840 gross a month. The amount will be prorated for part-time employees. &quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+4c&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:14,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:156},&quot;recordId&quot;:&quot;VeuFdhSdXoAJoYx5gwMcZxdxndC&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">Payroll Cost&nbsp;for Employers</h2><p>The employer cost is generally estimated at 16.5% of the employee salary.<br>  &#8211; Health insurance &#8211; 16.5%<br>  Conditional Costs:<br>Expenses Tax Gross-up: Variable depending on the employee’s salary. This is an additional amount of money added to cover the income taxes the employee will owe on the payment of reimbursed expenses.</p><h2 class="wp-block-heading has-large-font-size">Termination </h2><h3 class="wp-block-heading has-large-font-size">Grounds</h3><p>  Terminations must respect complex rules and the rules of an employee’s employment country. The off-boarding is always handled by the Employer with the primary stakeholders. It may include ad-hoc fees as well as required or recommended steps on specific termination cases.<br>Terminations in Croatia can be complex. There is no at-will termination in Croatia for employers, outside the probation period, and termination must be done for just cause.<br>Compliant terminations include:<br>  &#8211; Voluntarily by the employee<br>  &#8211; By mutual agreement<br>  &#8211; Unilaterally by the employer based on:<br>    &#8211; Economic, technical, or organizational reasons the need for Employee&#8217;s work position ceases to exist<br>    &#8211; Employee ceases to be capable of performing his/her duties, due to his/her ability or character<br>    &#8211; Employee breaches his/her obligations arising from the employment relationship<br>  &#8211; By the expiration of the contract</p><h2 class="wp-block-heading has-large-font-size">Notice Period</h2><p>The minimum notice period is 2 weeks and will be increased according to the length of the employment.</p><figure class="wp-block-table"><table><tbody><tr><td>Length of Employment</td><td>Notice Period</td></tr><tr><td>Probation Period</td><td>7 days</td></tr><tr><td>Less than 1 year</td><td>14 days</td></tr><tr><td>Consecutively worked for 1 year</td><td>14 days</td></tr><tr><td>Consecutively worked for 2 years</td><td>42 days</td></tr><tr><td>Consecutively worked for 5 years</td><td>56 days</td></tr><tr><td>Consecutively worked for 10 years</td><td>70 days</td></tr><tr><td>Consecutively worked for 20 years*</td><td>90 days</td></tr></tbody></table></figure><ul><li>The stated deadlines are reduced by half if the employee&#8217;s employment contract is terminated due to a breach of the employment obligation, i.e. if it is a case of dismissal due to the employee&#8217;s misconduct</li><li>If the employee terminates the employment contract, the notice period is one month</li><li>If the worker has reached 50 years of age this period is extended for another 2 weeks, and if the worker has reached 55 years of age for one month.</li></ul><h2 class="wp-block-heading has-large-font-size">Severance compensation</h2><p>In Croatia, employees are entitled to severance pay if the following conditions are met:<br>  &#8211; The employer terminates the employment contract<br>  &#8211; The dismissal is not conditioned by the behavior of the worker<br>  &#8211; The worker has spent at least 2 years continuously working for the employer terminating the contract<br>Severance payment is based on the length of employment with the same employer. The statutory minimum severance payment is one-third of the monthly salary (average of the last 3 months) per year of employment. The maximum payment is 6 months salary unless otherwise provided for by law, by-law, collective agreement, or work contract.</p><h2 class="wp-block-heading has-large-font-size">Payment &amp; leave Compensation&nbsp;&amp; Holidays </h2><h3 class="wp-block-heading has-large-font-size">SalaryPayment</h3><p>Monthly</p><h2 class="wp-block-heading has-large-font-size">Payslip&nbsp;Payroll</h2><p>No explicit provision</p><h2 class="wp-block-heading has-large-font-size">Annual Leave</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">Both full-time and part-time employees are entitled to 20 working days of paid time off (PTO) a year. PTO accrues monthly at 1.67 days per month which is pro-rated for part-time employees.</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;LfYZd4TeJo2PX3xn9mycaVkwnnd&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;Both full-time and part-time employees are entitled to 20 working days of paid time off (PTO) a year. PTO accrues monthly at 1.67 days per month which is pro-rated for part-time employees.&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+58&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:50,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:188},&quot;recordId&quot;:&quot;Bljid9wM0oLw1Fx1S4TckzYknbb&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">Sick Leave</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">Employees are entitled to paid sick leave for up to 42 days and long-term illness benefits after 42 days. This leave is paid at different rates and by different payers:</span></p><figure class="wp-block-table"><table><tbody><tr><td>Period</td><td>Pay (% of base salary)</td><td>Payer</td></tr><tr><td>First 42 days</td><td>A minimum of 70%, based on the employment agreement</td><td>Employer</td></tr><tr><td>After 42 days</td><td>Income replacement benefit is calculated</td><td>Employer reimbursed by Croatian Health Insurance Fund (HZZO)</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">Maternity &amp; Parental Leave</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">There is no paternity leave currently required by law.</span></p><p><span style="font-size: 16.8px;">Employees are entitled to 8 months for the first and second child and 30 months for twins or every child after the first two of parental leave. Both parents can use parental leave, each for 4 or 15 months. If this right is used by only one parent, then leave can last only 6 or 30 months. Salary compensation during the first 6 months of parental leave is paid 100% of their average salary (based on the last 6 months) with a maximum limit of HRK 5,654.20 per month. For the remaining leave, employees will receive 70% of their average salary.<br>The Croatian Health Insurance Fund (HZZO) is responsible for this payment.<br>Parental leave can&#8217;t be extended.</span><span data-lark-record-data="{&quot;rootId&quot;:&quot;LfYZd4TeJo2PX3xn9mycaVkwnnd&quot;,&quot;text&quot;:{&quot;initialAttributedTexts&quot;:{&quot;text&quot;:{&quot;0&quot;:&quot;There is no paternity leave currently required by law.&quot;},&quot;attribs&quot;:{&quot;0&quot;:&quot;*0+1i&quot;}},&quot;apool&quot;:{&quot;numToAttrib&quot;:{&quot;0&quot;:[&quot;author&quot;,&quot;7301936627151208452&quot;]},&quot;nextNum&quot;:1}},&quot;type&quot;:&quot;text&quot;,&quot;referenceRecordMap&quot;:{},&quot;extra&quot;:{&quot;channel&quot;:&quot;saas&quot;,&quot;mention_page_title&quot;:{},&quot;external_mention_url&quot;:{}},&quot;isKeepQuoteContainer&quot;:false,&quot;isFromCode&quot;:false,&quot;selection&quot;:[{&quot;id&quot;:29,&quot;type&quot;:&quot;text&quot;,&quot;selection&quot;:{&quot;start&quot;:0,&quot;end&quot;:54},&quot;recordId&quot;:&quot;B7FKdwNUaoZSagxzXCucksaonmf&quot;}],&quot;payloadMap&quot;:{},&quot;isCut&quot;:false}" data-lark-record-format="docx/text" class="lark-record-clipboard"></span></p><h2 class="wp-block-heading has-large-font-size">Tax and Social Security Information</h2><h2 class="wp-block-heading has-large-font-size">Personal Income Tax</h2><p><span style="text-indent: 2em; font-size: revert; background-color: var(--wp--preset--color--base); color: var(--wp--preset--color--contrast); font-family: var(--wp--preset--font-family--body);">Income tax rates in Croatia vary based on the residence of the employee. The following tax rates apply to monthly income:</span></p><figure class="wp-block-table"><table><tbody><tr><td></td><td>2024 Tax Rate</td></tr><tr><td>Monthly Income up to €4,200</td><td>15% &#8211; 23.6%</td></tr><tr><td>Monthly Income €4,201 and over</td><td>25% &#8211; 35.4%</td></tr><tr><td>Tax Free Allowance</td><td>€560</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">Social security</h2><ul><li>Social Security</li><li>Private Healthcare &#8211; Unisure (optional)</li></ul><h2 class="wp-block-heading has-large-font-size">Public Holidays 2024</h2><figure class="wp-block-table"><table><tbody><tr><td>holidays</td><td>date</td></tr><tr><td>New Year&#8217;s Day&nbsp;&nbsp;&nbsp;&nbsp;</td><td>1.1</td></tr><tr><td>Epiphany</td><td>1.6</td></tr><tr><td>Easter Sunday</td><td>3.31</td></tr><tr><td>Easter Monday</td><td>4.1</td></tr><tr><td>Labour Day</td><td>5.1</td></tr><tr><td>Statehood Day</td><td>5.30</td></tr><tr><td>Day of Antifascist Stuggle</td><td>6.22</td></tr><tr><td>Victory Day</td><td>8.5</td></tr><tr><td>Assumption of Mary</td><td>8.15</td></tr><tr><td>All Saints&#8217; Day</td><td>11.1</td></tr><tr><td>Remenberance Day</td><td>11.18</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr><tr><td>St Stephen&#8217;s Day</td><td>12.26</td></tr></tbody></table></figure>';
function submitSuccess() {
  ElMessage.success('お問い合わせを受け付けました。できるだけ早くご連絡いたします。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>