.contries {
  padding: 0 20px;

  .contries-banner {
    margin-bottom: 84px;
    position: relative;

    .banner {
      width: 100%;

      figure,img {
        width: 100%;
        height: 100%;
        display: block;
        border-radius: 6px;
      }
    }

    .flag {
      width: 140px;
      // height: 105px;
      position: absolute;
      left: 20px;
      bottom: -56px;
      border-radius: 6px;
      box-shadow: 0px 2px 3px 0px rgba(0,0,0,0.03);
      padding: 3px;
      background: white;
      box-sizing: content-box;

      img {
        width: 100%;
        height: 100%;
        display: block;
        border-radius: 4px;
      }
    }

    .contact-us {
      position: absolute;
      right: 0;
      bottom: -56px;
     
      button {
        width: 92px;
        height: 32px;
        background: #FF9111;
        border-radius: 4px;
        font-size: 12px;
        border: 0 none;
        color: #fff;
      }
    }
  }

  .contries-content {
    padding-bottom: 81px;

    h1 {
      font-size: 20px;
      font-weight: bold;
      color: #000;
      line-height: 24px;
    }
    .desc {
      font-size: 14px;
      color: rgba(0,0,0,0.75);
      margin-top: 8px;
      color: #000000;
      line-height: 18px;
    }

    h3 {
      margin-top: 32px;
      font-size: 17px;
      font-weight: bold;
      color: #111;
      line-height: 20px;
    }

    .list {
      margin-top: 20px;

      .list-item {
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        margin-top: 20px;
        padding-left: 19px;
        position: relative;

        &:last-child {
          margin-bottom: 20px;
        }

        &::before {
          content: '';
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background: #E67F06;
          position: absolute;
          left: 0px;
          top: 8px;
        }
        
        .list-item {
          margin-top: 8px;

          &::before {
            background: #fff;
            border: 1px solid #E67F06;
            box-sizing: border-box;
          }
        }
      }
    }
  }
}