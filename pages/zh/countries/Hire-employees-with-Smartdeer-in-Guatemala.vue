<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在危地马拉雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在危地马拉雇佣',
  ogDescription: '基础信息 首都：危地马拉城 Guatemala City 时区：GMT-6 语言：西班牙语 货币代码：GTQ […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：危地马拉城 Guatemala City 时区：GMT-6 语言：西班牙语 货币代码：GTQ […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在危地马拉雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在危地马拉雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/t013e81df2ab2c8520f.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/NnmYFj.png';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：危地马拉城 Guatemala City</p><p>时区：GMT-6</p><p>语言：西班牙语</p><p>货币代码：GTQ</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading has-large-font-size">雇佣合同</h3><p>合同必须是西班牙语，可以是双语的。它们必须是书面的，并由双方签署。<br>合同必须包括：</p><ul><li>名字</li><li>开始日期</li><li>雇佣期限</li><li>工作描述</li><li>终止条件</li></ul><h3 class="wp-block-heading">试用期相关规定</h3><p>试用期是强制性的。试用期为60天。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工作时间为每天8小时，每周40小时。标准工作周从星期一到星期五。但通常员工的工作周也包括44小时。</p><p>加班工资是强制性的，可能包含在工资中。超出标准工作时间的小时被视为加班。员工每天最多可以工作4小时的加班。对于额外的工时，员工薪资为：</p><ul><li>时薪的150%</li><li>公众假期、年假或常规假期的工作时薪的200%，并额外获得一天的假期</li></ul><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>最低工资为每月GTQ 2959.24。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>雇主成本通常估计为员工工资的29.3%。</p><ul><li>社会保障 (IGSS)：10.67%</li><li>工人娱乐协会 (IRTRA)：1%</li><li>专业培训协会 (INTECAP)：1%</li><li>13个月奖金：8.33%</li><li>圣诞奖金：8.33%</li><li>一次性行政注册费用：45美元</li><li>一次性强制“湿墨”雇佣协议处理费 &#8211; 80美元</li></ul><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading has-large-font-size">终止原因</h3><p>终止必须遵守复杂的规定和员工所在国家的规定。解雇始终由雇主与主要利益相关者处理。它可能包括特定终止情况下的特定费用或所需或建议的步骤。<br>危地马拉的终止是复杂的。在试用期外，危地马拉雇主不能随意解雇，解雇必须有正当理由。<br>符合规定的终止包括：</p><ul><li>员工自愿离职</li><li>经双方同意</li><li>雇主单方面基于：<ul><li>试用期</li><li>客观理由</li><li>纪律性解雇</li><li>由于不适合工作而绩效不佳</li></ul></li><li>合同到期</li></ul><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>最低通知期根据雇佣期限和终止类型而变化，从1周到1个月不等。</p><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>在危地马拉，没有解雇费用的法定要求。在通知期内，员工将收到标准工资。然而，如果员工被无故解雇，他们有30天的时间提出诉讼。如果成功，员工将有权获得1年的解雇费用。</p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading has-large-font-size">薪资支付</h3><p>每月一次</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>无明确规定</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职员工在连续工作一年后有权获得15个工作日的带薪休假。带薪休假每月累积1.25天。员工连续工作12个月后有资格获得年假。兼职员工的休假按工作时间比例分配。<br>危地马拉的带薪休假包括公共假期。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>危地马拉的病假较为复杂。员工有权获得带薪病假。这种假期的支付比例根据疾病的长度和员工的工资不同而不同。员工将获得社会保障直接支付的最高150格查尔一天。</p><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>员工连续工作四个月后有权获得84天的带薪假期。孩子出生前必须休假30天。员工在此期间将获得其工资的100%，社会保障将负责支付。</p><p>在危地马拉，没有法律涵盖育婴假。但员工有权享受产假和陪产假。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading has-large-font-size">个人所得税</h3><p>个人所得税范围从5%到7%不等。个人所得税根据渐进税率计算。</p><h2 class="wp-block-heading has-large-font-size">社会保险</h2><ul><li>国家工业培训基金</li><li>社会保障</li><li>私人医疗保健 &#8211; Unisure (可选)</li><li>私人医疗保健 &#8211; Allianz (可选)</li></ul><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>假期</td></tr><tr><td>New Year&#8217;s Day&nbsp;&nbsp;&nbsp;&nbsp;</td><td>1.1</td></tr><tr><td>Maundy Thursday </td><td>3.28</td></tr><tr><td>Good Friday</td><td>3.29</td></tr><tr><td>Easter Saturday</td><td>3.30</td></tr><tr><td>Labour Day</td><td>5.1</td></tr><tr><td>Mother&#8217;s Day</td><td>5.10</td></tr><tr><td>Army Day Holiday</td><td>7.1</td></tr><tr><td>Independence Day</td><td>9.15</td></tr><tr><td>Revolution of 1944</td><td>10.20</td></tr><tr><td>All Saints&#8217; Day</td><td>11.1</td></tr><tr><td>Christmas Eve</td><td>12.24</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr><tr><td>New Year&#8217;s Eve</td><td>12.31</td></tr></tbody></table></figure>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>