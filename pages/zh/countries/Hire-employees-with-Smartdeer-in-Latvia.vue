<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在拉脱维亚雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在拉脱维亚雇佣',
  ogDescription: '基础信息 首都：里加 Riga 时区：GMT+2 语言：拉脱维亚语 货币代码：EUR 人力资源制度概况 雇佣合 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：里加 Riga 时区：GMT+2 语言：拉脱维亚语 货币代码：EUR 人力资源制度概况 雇佣合 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在拉脱维亚雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在拉脱维亚雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/拉脱维亚-FI.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/拉脱维亚-国旗.jpg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：里加 Riga</p><p>时区：GMT+2</p><p>语言：拉脱维亚语</p><p>货币代码：EUR</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading">雇佣合同</h3><p>&nbsp;&nbsp;合同必须用拉脱维亚语书写，如果员工不懂官方语言，则可以是双语的。合同必须书面并由双方签署。</p><p>&nbsp;&nbsp;合同必须包括：</p><ul><li>员工详情（姓名，身份证号码，居住地址）</li><li>雇主详情（名称，身份证号码，法定地址）</li><li>开始日期</li><li>合同期限</li><li>工作地点</li><li>按职业分类的职位</li><li>试用期</li><li>工资金额和支付日期</li><li>每日和每周工作小时数</li><li>带薪年假的期限</li><li>终止条款</li></ul><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>试用期（或试用期）最长可为三个月，由双方商定。对于18岁以下的员工，不应设定试用期。</p><p>试用期内，雇主或雇员均可提前三天通知终止雇佣关系，无需说明原因。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>最长工作时间为每天 8 小时或每周 40 小时。工人有权在轮班之间至少休息 12 小时，工作 6 小时后可休息 30 分钟。对于轮班或夜班工人，晚上 10 点至早上 6 点之间的工作，每晚工作时间不能超过 7 小时。</p><p>7 天内每周休息时间不得少于连续 42 小时。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>从2024年1月1日开始，最低工资为每月700欧元。</p><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading">终止原因</h3><p>拉脱维亚的解雇可能很复杂。拉脱维亚雇主在试用期外无法随意解雇员工，解雇必须有正当理由。</p><p>合规的终止包括：</p><ul><li>员工自愿离职</li><li>双方协议</li><li>雇主单方面解雇（根据《劳动法》第101条规定的原因）</li></ul><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>最短通知期为一个月，如果在雇佣合同中约定，可以缩短。</p><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>&nbsp;&nbsp;在拉脱维亚，因以下原因被解雇的所有员工都有权获得解雇补偿金：</p><ul><li>员工缺乏适当的职业能力以履行工作</li><li>员工因健康状况而无法执行合同工作，且经医生意见证明了这种状况</li><li>以前从事相应工作的员工已被重新聘用</li><li>员工人数正在减少</li><li>雇主正在清算</li><li>员工因暂时性无法工作而不工作</li></ul><p>&nbsp;&nbsp; 解雇补偿金将取决于工作年限：</p><ul><li>少于五年的工作年限 &#8211; 平均收入的一个月</li><li>从5到10年的工作年限 &#8211; 平均收入的两个月</li><li>从10到20年的工作年限 &#8211; 平</li></ul><p>&nbsp;&nbsp;均收入的三个月</p><ul><li>超过20年的工作年限 &#8211; 平均收入的四个月</li></ul><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading">薪资支付</h3><p>除非雇主和雇员另有约定，雇主有义务每月至少支付两次报酬。报酬必须以当地货币（欧元）和现金支付，但如果雇主和雇员同意，也可以非现金支付。</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>工资单必须显示支付的薪酬、税款和强制性社会保险费用扣除额的计算。工资单必须详细列出加班、夜间或公共假日的工作时间。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职和兼职员工均有权每年获得20天的带薪休假（PTO）。带薪休假每天以约0.08个日历日的速度累积。员工在连续工作六个月后第一年有资格获得年假。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>拉脱维亚的员工有权获得无限数量的病假，但连续不得超过52周。如果员工连续病假六个月或在三年内总共病假一年，雇主可以在员工病假时通知员工，并在10天后终止劳动关系。</p><p>病假的支付方式如下：</p><figure class="wp-block-table"><table><tbody><tr><td>日</td><td>工资（工资的百分比）</td><td>付款人</td></tr><tr><td>第1天</td><td>未付款的</td><td>不适用</td></tr><tr><td>第 2 天和第 3 天</td><td>75%</td><td>雇主</td></tr><tr><td>第 4 &#8211; 9 天</td><td>80%</td><td>雇主</td></tr><tr><td>从第 10 天到第 26 周*</td><td>平均工资的 80%</td><td>国家社会保障局</td></tr></tbody></table></figure><p>*在严重病例中，病假最多可延长至52周，但支付相同的费用。</p><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>怀孕员工有权获得112天的带薪休假。孩子出生前必须休56天，孩子出生后也必须休56天。员工将获得平均工资的80%。</p><p>平均工资是根据休假前两个月结束的12个月期间计算的。国家社会保障局（SSA）将负责支付这笔款项。</p><p>员工可以通过育婴假延长休假。</p><p>自2023年1月1日起，员工可以在以下两种育婴假选项之间选择：</p><ul><li>孩子出生后13个月，支付工资为员工正常工资的60%：<ul><li>基本期间：一个父母（包括产假）在孩子满1岁之前休息9个月。</li><li>不可转让期：母亲休息2个月，父亲休息2个月，直到孩子满8岁。</li></ul></li><li>孩子出生后19个月，支付工资为员工正常工资的43.75%：<ul><li>基本期间：一个父母（包括产假）在孩子满1岁半之前休息15个月。</li><li>不可转让期：母亲休息2个月，父亲休息2个月，直到孩子满8岁。</li></ul></li></ul><p>基本期间（9或15个月）可以由父母中的一人休假。不可转让期必须在孩子满8岁之前休假，并且必须由双方父母休假。</p><p>国家社会保障局（SSA）将负责支付这笔款项。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading">个人所得税</h3><p>个人所得税范围从20%到31%不等。个人所得税按照渐进税率计算。</p><figure class="wp-block-table"><table><tbody><tr><td>年总收入 （欧元）</td><td>税率 （%）</td></tr><tr><td>高达 20,004 欧元</td><td>20%</td></tr><tr><td>20,004 欧元 &#8211; 78,100 欧元</td><td>23%</td></tr><tr><td>78,100欧元以上</td><td>31%</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">社会保险</h2><p>国家社会保险缴款（NSIC）由雇员和雇主每月向国家社会保险机构（VSAA）缴纳。这些缴款保证了员工的健康保险、养老金和失业保险，以及产假、陪产假和育儿假。雇员的税率为10.5%，雇主的税率为23.59%。</p><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>日期</td></tr><tr><td>New Year&#8217;s Day</td><td>1.1</td></tr><tr><td>Good Friday</td><td>3.29</td></tr><tr><td>Easter Monday</td><td>&nbsp;4.1</td></tr><tr><td>Labour Day</td><td>5.1</td></tr><tr><td>Independence Restoration Day</td><td>5.4</td></tr><tr><td>Midsummer Night&#8217;s Eve</td><td>&nbsp;6.23</td></tr><tr><td>St.John&#8217;s Day</td><td>&nbsp;6.24</td></tr><tr><td>ndependence Day</td><td>&nbsp; 11.18</td></tr><tr><td>Christmas Eve</td><td>&nbsp; 12.24</td></tr><tr><td>Christmas Day</td><td>&nbsp; 12.25</td></tr><tr><td>Second Day of Christmas</td><td>&nbsp; 12.26</td></tr><tr><td>New Year&#8217;s Eve</td><td>&nbsp;&nbsp; 12.31</td></tr></tbody></table></figure><p></p><p></p><p></p><p></p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>