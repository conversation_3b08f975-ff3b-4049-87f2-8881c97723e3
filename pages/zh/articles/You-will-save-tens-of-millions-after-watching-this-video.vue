<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-content
      h1.article-title 如果你是一家出海沙特的中资企业，看完这条视频你会节约几千万
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '如果你是一家出海沙特的中资企业，看完这条视频你会节约几千万',
  ogDescription: '如果你是一家出海沙特的中资企业，看完这条视频你会节约几千万。如果你在沙特开了公司，和当地人遇到官司纠纷，有可能 […]',
  ogSiteName: 'SmartDeer',
  description: '如果你是一家出海沙特的中资企业，看完这条视频你会节约几千万。如果你在沙特开了公司，和当地人遇到官司纠纷，有可能 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '如果你是一家出海沙特的中资企业，看完这条视频你会节约几千万'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '如果你是一家出海沙特的中资企业，看完这条视频你会节约几千万';
const bannerImage = '';
const flagImage = '';
const htmlContent = '<p>如果你是一家出海沙特的中资企业，看完这条视频你会节约几千万。如果你在沙特开了公司，和当地人遇到官司纠纷，有可能年薪千万的律师都没办法！为什么？给大家分享我们smartdeer接触到的一个真实的案例，给所有正在考虑出海沙特的企业家们提供一些参考借鉴，一个中国民营巨头企业在沙特设有一家分公司，该公司雇佣了一些沙特本地员工，其中有一名员工迟到早退，工作的质量非常低下，于是中国的管理层多次就工作态度和缺席的情况与该员工沟通，但效果不佳。在一次沟通中，管理层使用了可能冒犯宗教信仰的不礼貌的语言。该员工立刻将公司告上了法庭。该企业花了重金聘请了非常优秀的律师去打这场官司，但是还是输了。企业最后不得不赔偿该员工很大一笔钱。后面企业在树立当地正面形象的公关费用、罚款和其他法律费用上，最终花了上亿人民币。而败诉的主要原因就是走出去的中国企业管理层没有尊重当地员工，没有尊重当地法律要求，本身迟到、早退、缺勤是可以用合适的方法去处理的，但是企业却踩了不尊重当地文化传统的大坑，可惜没有提前和我们smartdeer联系，我们拥有全球150多个国家和地区的用工经验，并且对一些宗教国家的信仰也有足够多的了解，帮数十个企业避过坑，节约罚款和其他法律费用上亿元，smartdeer在这里也提醒大家，遇到冲突也不要用中国文化习惯惯例去处理，遇到法律纠纷一定要提前和我们沟通，严格遵守法律规范，避免一切潜在风险，否则将会影响一家企业的生存。</p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>