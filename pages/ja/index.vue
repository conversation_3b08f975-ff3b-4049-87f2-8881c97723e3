<template lang="pug">

  mixin header
    header
      //- .header-background
      //-   .decoration
      site-header(lang="ja" source="home" @contact-us="status.showForm = true")
  
      .header-content
        .slogon グローバル採用・海外雇用支援
        h1.site-title 海外人材採用・雇用のワンストップサービス
        .desc 
          .desc-text 世界中から優秀な人材を採用し、各国の雇用・給与に関するコンプライアンス対応を含む、プロフェッショナルな人事ソリューションをワンストップで提供します。
          .image
            figure
              img(src="~/assets/images/index/globle-min.webp")
  
  mixin customer
    section.customer
      .section-title
        h2 世界中のお客様をサポート
      CustomerList
  
  mixin service
    section.service
      .section-title
        h2 ソリューション
      .service-list
        .service-item(id="service-recruitment" v-scroll-show="{ delayOffset: 100 }")
          .figure-area
            figure
              img(src="~/assets/images/index/recruitment.webp" alt="Global Recruitment")
          .service-content
            .service-title
              h3 グローバル採用支援
            .service-desc
              p グローバルに人材を採用したい企業様に向けて、経験豊富な専門チームがターゲット市場でのスムーズな採用をサポートします。
              p 豊富な人材データベースと熟練のヘッドハンティングチームにより、採用から報酬管理まで効率的に対応。24時間365日のサポート体制で、運用も安心です。
              button.service-contact-button(@click="status.showForm=true") 詳細情報をリクエストする
                ArrowRight(class="inline-arrow")
        .service-item(id="service-eor" v-scroll-show="{ delayOffset: 100 }")
          .figure-area
            figure
              img(src="~/assets/images/index/eor.webp" alt="EOR")
          .service-content
            .service-title
              h3 海外雇用代行（EOR）サービス
            .service-desc
              p 海外での法人設立が不要。現地法に準拠しつつ、コストを抑えた正社員採用が可能です。
              p コンプライアンス対応、ビザ取得、入社手続き、チーム管理、給与計算、税務、福利厚生まで、すべてを一括サポート。自動化ツールによりレポート業務も効率化し、スムーズな運用を実現します。
              button.service-contact-button(@click="status.showForm=true") 詳細情報をリクエストする
                ArrowRight(class="inline-arrow")
        .service-item(id="service-contractor" v-scroll-show="{ delayOffset: 100 }")
          .figure-area
            figure
              img(src="~/assets/images/index/contractor.webp" alt="Contractor")
          .service-content
            .service-title
              h3 業務委託契約（フリーランス）支援
            .service-desc
              p 新たな市場で短期的かつ柔軟な雇用形態をご希望ですか？現地に法務チームがなくても、迅速に業務委託人材の確保を支援します。
              p 各国法に準拠した契約テンプレートとスムーズなオンライン締結プロセスを提供。リスク回避のためのバックグラウンドチェックや、明瞭な料金体系による報酬支払いサービスも完備しています。
              button.service-contact-button(@click="status.showForm=true") 詳細情報をリクエストする
                ArrowRight(class="inline-arrow")
        .service-item(id="service-peo" v-scroll-show="{ delayOffset: 100 }")
          .figure-area
            figure
              img(src="~/assets/images/index/peo.webp" alt="Human Resource Outsourcing")
          .service-content
            .service-title
              h3 人事業務アウトソーシング
            .service-desc
              p 人事業務の負担を軽減したい現地法人設立済みの企業様へ。給与計算、税務対応、社会保険、福利厚生、入社手続きなどを一括して代行します。
              p 貴社のニーズに合わせて柔軟にカスタマイズ可能。各国の法令遵守を徹底し、業務効率化とコスト削減を同時に実現します。
              button.service-contact-button(@click="status.showForm=true") 詳細情報をリクエストする
                ArrowRight(class="inline-arrow")
        .service-item(id="service-fintech" v-scroll-show="{ delayOffset: 100 }")
          .figure-area
            figure
              img(src="~/assets/images/index/fintech.png" alt="FinTech-Driven Global Payroll Solutions")
          .service-content
            .service-title
              h3 FinTech活用のグローバル給与ソリューション
            .service-desc
              p SmartDeerのプラットフォームは、150種類以上の通貨に対応し、世界中の従業員や業務委託者への給与をスムーズかつ効率的に支払うことができます。競争力のある為替レート、強力な為替リスクヘッジ機能、一括支払い機能により、国際送金コストを大幅に削減し、為替変動による給与コストの影響を最小限に抑えます。
              p また、SmartDeerはコンプライアンスや税務管理の専門知識を活かし、グローバル人事運営を円滑に支援。国際雇用における信頼できるパートナーとしてご活用いただけます。
              button.service-contact-button(@click="status.showForm=true") 詳細情報をリクエストする
                ArrowRight(class="inline-arrow")
        .service-item(id="service-fintech" v-scroll-show="{ delayOffset: 100 }")
          .figure-area
            figure
              video(id="video-player" class="video-js" preload="auto" controls="true" poster="~/assets/images/index/video-bg.jpg")
                source(src="https://static.smartdeer.com/Global_HR_SaaS.mp4", type="video/mp4")
          .service-content
            .service-title
              h3 グローバル人事SaaSプラットフォーム
            .service-desc
              p SmartDeerは、「人事サービス＋SaaSシステム」モデルを通じて、グローバル展開を目指す企業に対し、専門的かつ統合的なソリューションを提供します。SmartDeerのグローバル人事SaaSプラットフォームは、データに基づいたデジタル化された人事管理を実現し、企業のグローバルな人事戦略を力強く支援します。
              p 世界中の従業員、人事チーム、マネージャーが各種人事業務や関連タスクを効率的に管理できるよう支援します。人事データを一元管理することで、業務の効率化と最適化を実現します。
              button.service-contact-button(@click="status.showForm=true") 詳細情報をリクエストする
                ArrowRight(class="inline-arrow")
  mixin advantage
    section.advantage
      .section-title
        h2 SmartDeerが選ばれる理由
  
      .advantage-list()
        .advantage-item()
          figure.advantage-icon-area
            img(src="~/assets/images/index/icon-global.svg" alt="覆盖150+个国家")
          .advantage-title グローバルネットワーク
          .advantage-content 150カ国以上に対応、24時間365日体制でビジネスをサポート。
  
        .advantage-item()
          figure.advantage-icon-area
            img(src="~/assets/images/index/icon-professional-team.svg" alt="覆盖150+个国家")
          .advantage-title コンプライアンス対応
          .advantage-content 各国の法令やデータ保護規制を全プロセスで遵守。
  
        .advantage-item()
          figure.advantage-icon-area
            img(src="~/assets/images/index/icon-service.svg" alt="72小时全中文响应")
          .advantage-title 24/7サポート
          .advantage-content 24時間サポート 中国語・英語対応のバイリンガルサポートで迅速な対応を実現。
  
        .advantage-item()
          figure.advantage-icon-area
            img(src="~/assets/images/index/icon-price.svg" alt="极具竞争优势的价格")
          .advantage-title 競争力ある価格設定
          .advantage-content 高品質なサービスを最適なコストでご提供。
  
  mixin lifecycle
    section.lifecycle
      .section-title
        h2 人材ライフサイクルの一括管理
  
      .lifecycle-list
        .lifecycle-list-container
          .lifecycle-item(:data-nth="lifecycleItems[3]")
            figure
              img(src="~/assets/images/index/recruitment.svg")
            .title 採用
  
          .lifecycle-item(:data-nth="lifecycleItems[2]")
            figure 
              img(src="~/assets/images/index/compliance.svg")
            .title コンプライアンス対応
  
          .lifecycle-item(:data-nth="lifecycleItems[1]")
            figure 
              img(src="~/assets/images/index/contract.svg")
            .title 契約締結
  
          .lifecycle-item(:data-nth="lifecycleItems[0]")
            figure 
              img(src="~/assets/images/index/on-boarding.svg")
            .title 入社手続き
  
          .lifecycle-item(:data-nth="lifecycleItems[6]")
            figure 
              img(src="~/assets/images/index/management.svg")
            .title 従業員管理
  
          .lifecycle-item(:data-nth="lifecycleItems[5]")
            figure 
              img(src="~/assets/images/index/payment.svg")
            .title 給与支払い
  
          .lifecycle-item(:data-nth="lifecycleItems[4]")
            figure 
              img(src="~/assets/images/index/off-boarding.svg")
            .title 退職・契約終了対応
  
  mixin process
    section.process
      .section-title
        h2 SmartDeerの採用支援の仕組み
      .process-list
        .process-item
          .process-head
            .process-head-wrapper
              figure
                img(src="~/assets/images/index/ja/recriument.png")
              .num 01
          .process-content
            h3.title グローバルなサービス提供 × 業界特化型のローカライズ採用支援
            .desc 中国、東南アジア、北米、中東などに80名以上の採用コンサルタントを擁し、IT・SaaS・ゲーム・スマート製造など多様な業界に特化。オペレーション、事業開発、営業、エンジニア職まで、企業ニーズに応じたグローバルな人材採用を支援します。
  
        .process-item
          .process-head
            .process-head-wrapper
              .num 02
              figure
                img(src="~/assets/images/index/ja/compliance.png")
          .process-content
            h3.title ビザ取得支援・コンプライアンス対応
            .desc ビザ取得支援からバックグラウンドチェック、各国の法令対応まで、ワンストップで対応。スムーズな入社手続きを実現し、法的リスクの最小化と安全な雇用をサポートします。
  
        .process-item
          .process-head
            .process-head-wrapper
              figure
                img(src="~/assets/images/index/ja/contract.png")
              .num 03
          .process-content
            h3.title 契約テンプレートを選びカスタマイズすれば、作成から電子署名までわずか3分で完了
            .desc ビジネスモデルや雇用形態に応じて、コンプライアンス対応済みの契約テンプレートを選択。作成から電子署名まで、オンライン上で3分以内に完結します。
  
        .process-item
          .process-head
            .process-head-wrapper
              .num 04
              figure
                img(src="~/assets/images/index/ja/progress.png")
          .process-content
            h3.title 自動監査でオンボーディングを効率化
            .desc 従業員情報をアップロードするだけで、自動的にプラットフォームが審査を実行。オンラインオンボーディングシステムにより、迅速な承認が可能となり、スムーズに業務を開始できます。
  
        .process-item
          .process-head
            .process-head-wrapper
              figure
                img(src="~/assets/images/index/ja/staff.png")
              .num 05
          .process-content
            h3.title オンライン契約管理、従業員記録、休暇・勤怠管理
            .desc オンライン契約管理、従業員記録、休暇・勤怠管理を効率的に管理。複数の打刻方法をサポートし、承認プロセスを自動化し、実時間での出勤管理を実現。
  
        .process-item
          .process-head
            .process-head-wrapper
              .num 06
              figure
                img(src="~/assets/images/index/ja/pay.png")
          .process-content
            h3.title 報酬・給与・保険処理の一括対応
            .desc セルフサービスツールにより、経費精算・手当申請・承認プロセスを効率化。地域ごとの法規制に準拠しながら、給与管理を自動化。複数通貨での支払いにも対応し、透明性のある為替レートで運用可能です。
  
        .process-item
          .process-head
            .process-head-wrapper
              figure
                img(src="~/assets/images/index/ja/dimission.png")
              .num 07
          .process-content
            h3.title 退職手続きと最終清算の効率的な一元管理
            .desc 退職申請のスケジューリングから手続きの自動化、最終支払いまでをプラットフォーム上で一元管理。退職プロセスの各段階で、現地法令への確実な準拠を保証します。
  
  mixin solutionCase
    section.solution(id="service-solution")
      .section-title
        h2 ソリューション事例
        p Solution
      .solution-list
        .solution-item.case1-bg(v-scroll-show="{ delayOffset: 180 }")
          .solution-wrapper
            .solution-figure
              figure
                img(src='~/assets/images/index/case1.png')
            .solution-content(:class="{collapsed: !solutionExpandStatus.s1}")
              .solution-title
                h3 クライアント成功事例：中国大手ICTソリューション企業におけるグローバルチーム拡大と管理のベストプラクティス
              .solution-desc(:style="getSolutionDescStyle('s1')")
                h4 背景と課題
                p 2020年にグローバル展開を開始して以来、30以上の国・地域に進出し、以下のような具体的な課題に直面しました。
                ol
                  li #[strong 海外雇用：] 16カ国でのEORおよびビザ手続き、5カ国での人事アウトソーシング対応。
                  li #[strong 雇用コスト算定：] 複数国における正確な雇用コスト算出とコンプライアンス対応に関する専門的アドバイス。
                  li #[strong ポリシーアドバイザリーと迅速な対応：] 変化する各国の雇用政策に即応可能な体制づくり。
                  h4 直面した主な課題：
              ul
                li #[strong 複雑な事務処理：] 海外人事チームは、迅速かつ正確な対応を要する日々の問合せ業務に追われていました。
                li #[strong ビザ申請の難航：] 対象国ではリスクが高く、複雑なビザ申請手続きが必要で、企業担当者に大きな負担がかかっていました。
                li #[strong 精算業務の負荷：] 各部門から集中して寄せられる大量の精算申請により、人事・経理部門は大きな負荷を抱えていました。
              h4 SmartDeerが提供したソリューション
              p グローバル展開に伴う課題に対応するため、SmartDeerは以下の包括的ソリューションを構築・提供しました。
              ol
                li #[strong EORおよびHROサービス]
                  ul
                    li 16か国においてEOR（Employer of Record）サービスおよびビザ申請支援を実施。
                    li 5か国での現地化されたHRO（Human Resources Outsourcing）サービスを提供。給与計算、福利厚生、労働契約管理などをカバー。
                li #[strong リアルタイム・ポリシーアドバイザリー]
                  ul
                    li SmartDeerの専門チームが、各国の雇用・税制ポリシーについてリアルタイムに助言。採用戦略の迅速な最適化を支援。
                li #[strong ビザ申請サポートサービス]
                  ul
                    li 書類作成から提出まで、ビザ申請を一括でサポート。特に手続きの複雑な国を重点的にサポートし、ビザ承認率の向上を実現。
                li #[strong データ管理の自動化]
                  ul
                    li 自社プラットフォームによるデータの自動入力・分析により、精算業務のフローを最適化し、手作業の負担を軽減。
                li #[strong 迅速対応を実現するサポート体制]
                  ul
                    li 専任アカウントマネージャーと24時間365日のサポートチームを配置し、複雑な課題にも最大24時間以内で対応できる体制を構築。
              h4 導入効果
              ul
                li #[strong グローバル業務の効率化：] 16か国でのチーム採用およびビザ取得をサポートし、スムーズな市場進出を実現。
                li #[strong 時間コストを40％削減：] 自動化により人事部門の管理業務を大幅に削減。
                li #[strong ビザ承認率95％：] 高難度地域においても専門サポートで高い承認率を達成。
                li #[strong 精算処理効率が50％向上：] データの自動処理により、各部門の精算業務を大幅に最適化。
              h4 お客様の声
              p 「SmartDeerは、当社のグローバル拡大において包括的な支援を提供してくれました。雇用サービスからビザ取得に至るまで、高い専門性と効率性を発揮。国際業務における戦略的パートナーとして不可欠です。」 -— #[strong 国際事業部長]
              .solution-expand
                button.solution-toggle(@click="toggle('s1')")
                  span(v-if="!solutionExpandStatus.s1") 拡大
                  span(v-else) 折りたたみ
                  ArrowDown(v-if="!solutionExpandStatus.s1" class="inline-arrow")
                  ArrowUp(v-else class="inline-arrow")
        .solution-item.case2-bg(v-scroll-show="{ delayOffset: 180 }")
          .solution-wrapper
            .solution-figure
              figure
                img(src='~/assets/images/index/case2.png')
            .solution-content(:class="{collapsed: !solutionExpandStatus.s2}")
              .solution-title
                h3 中国大手ゲーム開発企業、日本市場での成功事例
              .solution-desc(:style="getSolutionDescStyle('s2')")
                h4 背景
                p 2022年に日本法人を設立したこの新興の中国系ゲーム企業は、2023年より本格的に日本市場への進出を開始。初の海外展開として、迅速な現地チーム構築や、日本の厳格な労働法・複雑な人事要件への対応という大きな課題に直面しました。
                h4 具体的な課題：
                ul
                  li #[strong 現地人事の専門知識不足：] 日本の労務コンプライアンスや従業員管理に精通した社内人事チームが存在しませんでした。
                  li #[strong 複雑な労働法規制：] 日本の厳格な労働関連法により、あらゆる人事管理業務で法令順守が求められ、法的リスク回避のための精密な運用が必要でした。
                h4 SmartDeerが提供したソリューション
                p SmartDeerは企業の初期段階のニーズと課題に対して、包括的かつカスタマイズされたソリューションを提供しました：
                ol
                  li #[strong 包括的なHROサービス]
                    ul
                      li 地元規制に準拠する従業員管理ポリシーを策定・提出。
                      li オンボーディングの業務フローを最適化し、必要な人事関連書類を整備。
                      li 従業員の個人情報に関する日本の法令に準拠したデータ利用方針を策定・運用。
                  li #[strong 現地における法務・コンプライアンス支援]
                    ul
                      li 雇用契約、給与計算、社会保険手続きについての実務支援を提供。
                      li 労働法改正など最新の法務情報をリアルタイムで提供し、日本の法的環境での事業運営を支援。
                  li #[strong 採用・チームビルディング支援]
                    ul
                      li SmartDeerの広範な現地ネットワークを活用し、優秀人材の採用を推進。
                      li 事業立ち上げに向けた実践的なチーム体制を迅速に構築・支援。
                  li #[strong カスタマイズ対応の支援サービス]
                    ul
                      li スタートアップフェーズに合わせた現地支援を通じて、日本市場への円滑な立ち上げを実現。
                h4 成果内容
                ul
                  li #[strong 迅速な現地チームの構築：] 現地チームを3ヶ月以内に採用・オンボーディングし、計画通りに事業を開始。
                  li #[strong コンプライアンス保証：] 日本の労働法規に完全準拠し、法的リスクを未然に防止。
                  li #[strong 時間・コストを40％削減：] 人事プロセスの自動化と効率化により、業務負荷と経費を大幅に削減。
                h4 お客様の声
                p 「SmartDeerのチームは、標準的なソリューションのみならず、日本市場への初進出時のニーズを深く理解したカスタマイズ支援も提供してくれました。専門性とプロフェッショナルさのおかげで、ゼロからイチへの道のりが非常にスムーズかつ効率的でした。」—  #[strong -- クライアント企業 人事責任者]
              .solution-expand
                button.solution-toggle(@click="toggle('s2')")
                  span(v-if="!solutionExpandStatus.s2") 拡大
                  span(v-else) 折りたたみ
                  ArrowDown(v-if="!solutionExpandStatus.s2" class="inline-arrow")
                  ArrowUp(v-else class="inline-arrow")
        .solution-item.case3-bg(v-scroll-show="{ delayOffset: 180 }")
          .solution-wrapper
            .solution-figure
              figure
                img(src='~/assets/images/index/case3.png')
            .solution-content(:class="{collapsed: !solutionExpandStatus.s3}")
              .solution-title
                h3 スマートロボティクス大手企業によるグローバル給与管理の効率化事例
              .solution-desc(:style="getSolutionDescStyle('s3')")
                h4 背景
                p スマートロボティクス分野で世界的なリーダーである同社は、近年さまざまな業界・地域へと急速に事業を拡大。国際チームの増加に伴い、グローバル給与管理において以下のような課題に直面しました：
                ol
                  li #[strong 多様な給与規制：] 国ごとに異なる給与法、税制、福利厚生制度への対応。
                  li #[strong 支払いチャネルの分散：] 国際送金における複数の支払手段の管理により、時間とコストが増加。
                  li #[strong チーム管理の負荷：] 広域・大規模な給与計算とデータ管理で人事部門に大きな負担。
                h4 主な課題：
                ul
                  li #[strong コンプライアンス保証：] 各国の法規に準拠した給与プロセスを徹底し、法的リスクを軽減。
                  li #[strong コスト最適化：] 為替変動や国際送金手数料の影響を抑え、給与コストを低減。
                  li #[strong 効率性向上：] グローバルにおける給与支払いの迅速化と処理効率の強化。
                h4 SmartDeerが提供するソリューション：
                ol
                  li #[strong グローバル給与サービス]
                    ul
                      li SmartDeerプラットフォームを使った多通貨対応の給与計算・支払いを一元化し、国際決済の業務フローを簡素化。
                  li #[strong 税務・法令対応支援]
                    ul
                      li 各国の最新給与・税制情報をリアルタイムで監視・適用し、法令順守を確実にサポート。
                  li #[strong 為替管理サービス]
                    ul
                      li 為替変動リスクを抑える通貨ロックサービスを提供し、支払いコストを最小限に。
                  li #[strong 給与管理の自動化]
                    ul
                      li AI／自動化ツールを活用した給与計算・支払いシステムを導入し、HR業務の定型作業を削減、業務効率を大幅に改善。
                  li #[strong カスタムレポート＆分析機能]
                    ul
                      li 詳細な給与レポートと多次元分析機能により、グローバル人件費のコントロールを強力に支援。
                h4 成果内容
                ul
                  li #[strong コンプライアンスリスクゼロ：] 10か国以上の給与管理を法令に完全準拠して実施。
                  li #[strong 30％のコスト削減：] 為替最適化と決済集中化により国際給与コストを大幅削減。
                  li #[strong 50％の効率向上：] 給与自動化ツールにより人事業務負荷を軽減し、処理効率が向上。
                  li #[strong 従業員満足度向上：] グローバルチームへの給与支払いを遅延なく実施し、定着率と満足度の向上を実現。
                h4 お客様の声
                p 「SmartDeerのグローバル給与管理サービスにより、コンプライアンスと効率性を確保しつつ、急速な事業拡大が可能になりました。プロフェッショナルなチームと高度なツールは、当社のグローバル戦略において不可欠な存在です。」 #[strong -- クライアント企業 人事責任者]
              .solution-expand
                button.solution-toggle(@click="toggle('s3')")
                  span(v-if="!solutionExpandStatus.s3") 拡大
                  span(v-else) 折りたたみ
                  ArrowDown(v-if="!solutionExpandStatus.s3" class="inline-arrow")
                  ArrowUp(v-else class="inline-arrow")
        .solution-item.case4-bg(v-scroll-show="{ delayOffset: 180 }")
          .solution-wrapper
            .solution-figure
              figure
                img(src='~/assets/images/index/case4.png')
            .solution-content(:class="{collapsed: !solutionExpandStatus.s4}")
              .solution-title
                h3 中国大手生鮮食品EC企業による中東市場進出支援事例：採用から雇用までのトータルサポート
              .solution-desc(:style="getSolutionDescStyle('s4')")
                h4 背景
                p 中国を代表する生鮮食品EC企業の一つである同社は、国内市場での成功を背景に、2023年に中東市場への進出を開始。サウジアラビアにおける初期運営を支えるため、中国からの幹部派遣に加え、調達・業務・ビジネス分析・マーケティングの現地チーム構築が急務となりました。
                h4 主な課題
                ul
                  li #[strong 法人設立・コンプライアンス：] サウジアラビアに現地法人がない状態での直接雇用は法的リスクがありました。また、サウジ化（Saudization）など、現地の労働法に基づく国籍比率基準を満たす必要がありました。
                  li #[strong 高度な採用ニーズへの対応：] 中東地域では人材供給に偏りがあり、調達・オペレーション・事業分析・マーケティングなど専門領域における高スキル人材の確保が困難でした。
                  li #[strong 越境人材の派遣管理：] 中国からサウジアラビアへの社員派遣には、ビザ申請、就労カードの発行、現地法令の遵守など、複雑な手続きが必要でした。
                h4 SmartDeerが提供したソリューション
                p SmartDeerは、企業のニーズに合わせたカスタマイズされたソリューションを提供しました：
                ol
                  li #[strong EOR（雇用代行）サービスの提供]
                    ul
                      li SmartDeerの現地法人がスタッフを直接雇用することで、クライアントによる法人設立が不要となりました。
                      li サウジ化要件を回避しつつ法令に則った運営を確立し、サウジアラビアおよび中東地域での迅速な事業立ち上げが可能となりました。
                  li #[strong 中東域内での採用支援]
                    ul
                      li SmartDeerの中東地域ネットワークを活用し、調達・オペレーション・事業分析・マーケティングなどの重要ポジションを迅速に採用。サウジアラビアを含む主要市場をカバーしました。
                  li #[strong 越境派遣の一元管理]
                    ul
                      li 中国から派遣される社員について、ビザ申請・就労カード取得・労務書類の準備まで、フルプロセスのサポートを提供し、効率的かつ法令準拠の派遣を実現しました。
                  li #[strong 日常支援とアドバイザリー体制]
                    ul
                      li SmartDeer現地スタッフが労働契約管理、給与処理、法規対応などの毎日のサポートを提供し、中東でのHR業務負担を軽減しました。
                h4 成果
                ul
                  li #[strong 人材体制の迅速構築：] 2か月以内に中東域内の主要ポジションの採用を完了し、10名の中国人社員をサウジアラビアへ派遣、円滑な事業運営を支援しました。
                  li #[strong コンプライアンスリスクゼロ：] SmartDeerの法人雇用モデルにより、サウジアラビアおよび中東地域で完全な法令順守を実現しました。
                  li #[strong 時間＆コストの最適化：] 現地法人設立が不要となり、大幅な時間短縮とコスト削減を実現し、クライアントは本業に集中できました。
                  li #[strong 地域展開の効率化：] SmartDeerの包括的支援により、サウジアラビアおよび中東での市場展開がスムーズに進み、初期の運営目標を達成しました。
                h4 お客様の声
                p 「SmartDeerのサポートにより、サウジアラビアおよび中東への進出がスムーズに進みました。現地採用の課題を克服し、越境派遣も包括的にサポートいただき、すべてのプロセスが法令準拠かつ効率的でした。グローバル展開における信頼できるパートナーです。」 #[strong -- HR責任者、クライアント企業]
              .solution-expand
                button.solution-toggle(@click="toggle('s4')")
                  span(v-if="!solutionExpandStatus.s4") 拡大
                  span(v-else) 折りたたみ
                  ArrowDown(v-if="!solutionExpandStatus.s4" class="inline-arrow")
                  ArrowUp(v-else class="inline-arrow")
        .solution-item.case5-bg(v-scroll-show="{ delayOffset: 180 }")
          .solution-wrapper
            .solution-figure
              figure
                img(src='~/assets/images/index/case5.png')
            .solution-content(:class="{collapsed: !solutionExpandStatus.s5}")
              .solution-title
                h3 グローバルブロックチェーン企業によるEOR導入事例：多国雇用への包括支援
              .solution-desc(:style="getSolutionDescStyle('s5')")
                h4 背景
                p ブロックチェーン技術のグローバルリーダーとして、分散型インターネット基盤の構築と、世界中の優秀な技術・業務人材の獲得を推進している本企業は、急速な事業拡大に伴い、多国籍なチームの雇用において以下の課題に直面しました。
                h4 主な課題
                ul
                  li #[strong 多国コンプライアンス対応：] 各国で異なる労働法・税制・社会保障制度に準拠したグローバル採用プロセスの整備が必要でした。
                  li #[strong 効率化とコスト最適化：] 迅速な採用・オンボーディングと同時に、管理・運用コストの最小化が求められました。
                  li #[strong 多通貨給与管理：] 為替変動によるリスクを伴う多通貨での給与支払い対応が課題でした。
                h4 SmartDeerが提供したソリューション
                p SmartDeerは、グローバルEOR（雇用代行）ワンストップソリューションを提供し、複数国でのチーム拡大を迅速かつ法令遵守のもとで実現しました。
                ol
                  li #[strong コンプライアンス管理]
                    ul
                      li SmartDeerのグローバルネットワークを活用し、各国での合法的な従業員雇用を支援。
                      li 労働契約・給与処理のすべてを現地法令に完全準拠させました。
                  li #[strong 迅速な従業員オンボーディング]
                    ul
                      li 契約締結・社会保険登録・税務処理など、オンボーディング全体を効率化し、早期戦力化を実現。
                  li #[strong 多通貨対応の給与支払いサービス]
                    ul
                      li SmartDeerプラットフォームにより、150種類以上の通貨での給与支払いに対応。
                      li 為替変動リスクを軽減する通貨ロック機能も統合し、財務面の安定性を確保。
                  li #[strong 継続的な人事サポート]
                    ul
                      li ポリシーアドバイス、従業員管理、契約更新などの継続支援を通じて、クライアントの人事負担を軽減しました。
                h4 導入成果
                ul
                  li #[strong グローバル拡大を迅速化：] 3か月以内に複数国でのチーム構築を実現し、グローバル成長戦略を後押し。
                  li #[strong コンプライアンス保証：] すべての雇用・給与業務を現地法に準拠させ、法務・税務リスクを回避。
                  li #[strong コスト30％削減：] 人事業務の一元化により、時間・人件費の両面で大幅削減を実現。
                  li #[strong 従業員満足度の向上：] 正確で迅速な給与支払いと充実したHRサポートにより、従業員の満足度を高め、長期的な定着につなげました。
                h4 お客様の声
                p 「SmartDeerのグローバルEORサービスにより、各国での採用やコンプライアンスの複雑さを気にせず、本業に集中することができています。プロフェッショナルな対応と効率的なサービスは、当社のグローバル展開を支える重要な柱です。」 #[strong -- クライアント企業 人事責任者]
              .solution-expand
                button.solution-toggle(@click="toggle('s5')")
                  span(v-if="!solutionExpandStatus.s5") 拡大
                  span(v-else) 折りたたみ
                  ArrowDown(v-if="!solutionExpandStatus.s5" class="inline-arrow")
                  ArrowUp(v-else class="inline-arrow")
        .solution-item.case6-bg(v-scroll-show="{ delayOffset: 180 }")
          .solution-wrapper
            .solution-figure
              figure
                img(src='~/assets/images/index/case6.png')
            .solution-content(:class="{collapsed: !solutionExpandStatus.s6}")
              .solution-title
                h3 大手オンライン教育プラットフォームによる香港HRO導入事例：人事業務の効率化支援
              .solution-desc(:style="getSolutionDescStyle('s6')")
                h4 背景
                p 中国大手のオンライン教育プラットフォームである同社は、世界中の学生に高品質な教育リソースを提供。国際展開を加速する中で、グローバルな業務拠点として香港にオペレーションセンターを設立しましたが、香港特有の人事管理要件と複雑性に直面しました。
                h4 主な課題
                ul
                  li #[strong 給与・法定福利の管理：] 給与処理や強制加入制度（MPFなど）への対応、すべての業務を現地法令に準拠して運用する必要がありました。
                  li #[strong データ管理とコンプライアンス：] 従業員情報や給与データを一元管理しながら、香港の個人情報保護法および労働法への対応が求められました。
                  li #[strong 人事業務の効率化：] テクノロジーを活用し、事務コスト削減と人事オペレーションの最適化を実現する必要がありました。
                h4 SmartDeerが提供したソリューション
                ol
                  li #[strong 給与・福利厚生管理サービス]
                    ul
                      li SmartDeerが給与計算および支払い処理を担当し、正確かつタイムリーな給与支給を実現。
                      li 強制積立年金（MPF）やその他の法定福利も適切に処理、法令順守を徹底し、コンプライアンスリスクの軽減を実現しました。
                  li #[strong HR SaaSプラットフォームの提供]
                    ul
                      li 従業員情報、勤怠、給与データ、契約記録などを一元管理できるHR SaaSプラットフォームを導入。
                      li リアルタイムでのデータ更新および自動レポート生成機能により、手作業によるミスを最小限に抑え、業務効率を向上。
                  li #[strong 日常人事サポートサービス]
                    ul
                      li 香港の労働法および政策変更に関するアドバイスを含めた実務サポートを提供し、最新情報を常に共有。
                      li 入社・退職プロセスの対応を支援し、すべての手続きを法令に準拠して実施。
                h4 導入成果
                ul
                  li #[strong 管理効率40％向上：] HR SaaS導入により給与・データ管理の業務フローが最適化され、人事部門の日常業務が大幅に軽減。
                  li #[strong コンプライアンスリスクゼロ：] 給与・福利厚生のプロ管理により法的リスクを排除し、従業員満足度と定着率を向上。
                  li #[strong コスト30％削減：] HROの外部委託により運用コストを大幅削減し、リソースを事業成長に集中可能に。
                  li #[strong リアルタイムなデータ管理：] 従業員データを一元的にデジタル管理し、意思決定を支援。
                h4 お客様の声
                p 「SmartDeerの香港におけるHROサービスとHR SaaSプラットフォームは、当社の人事管理効率を大幅に向上させました。給与や法定福利に関する専門性は非常に高く、グローバル展開に欠かせないパートナーです。」 #[strong -- クライアント企業 人事責任者]
              .solution-expand
                button.solution-toggle(@click="toggle('s6')")
                  span(v-if="!solutionExpandStatus.s6") 拡大
                  span(v-else) 折りたたみ
                  ArrowDown(v-if="!solutionExpandStatus.s6" class="inline-arrow")
                  ArrowUp(v-else class="inline-arrow")
  
  mixin contactForm
    .contact-form
      client-only
        el-dialog(v-model="status.showForm" title="" :width="354")
          contact-us-form(lang="ja" @submit="submitSuccess")
  
  mixin anchor
    .anchor
      .consultant(@click="status.showConsultantCode = !status.showConsultantCode")
        figure
          img(src="~/assets/images/index/anchor-avatar-en.png" )
      .consultant-code(v-show="status.showConsultantCode")
        .close(@click="status.showConsultantCode = false")
        figure
          img(src="~/assets/images/index/anchor-code-en.png" )
  
  mixin botButton
    .bot-container(@click="toggleChat")
      img(src="~/assets/images/index/bot_logo_en.png")
  
  mixin goTop
    .go-top-container(@click="smoothScrollTo(500, 0)")
      img(src="~/assets/images/index/top_icon.png")
  
  .index-page
    +header 
    +customer 
    +service 
    +advantage
    +lifecycle
    +process
    +solutionCase
    +contactForm
    site-footer(lang="ja" @contact-us="()=>{status.showForm = true}")
    +botButton
    +goTop
  </template>
  
  <script lang="ts" setup>
  import { ref } from 'vue'
  import { ElDialog, ElForm, ElFormItem, ElButton, ElInput, ElSelect, ElOption, ElMessage, ElDropdown, ElDropdownMenu, ElDropdownItem, ElCarousel, ElCarouselItem } from 'element-plus'
  import phoneArea from '~/assets/utils/global-phone-area'
  import langTool from '~/assets/utils/lang'
  import CustomerList from '@/components/customer-list.vue'
  import { getQueryString } from '~/assets/utils'
  import videojs from "video.js";
  import {ArrowUp, ArrowDown, ArrowRight} from '@element-plus/icons-vue';

  definePageMeta({ layout: 'basic' })

  // 获取运行时配置
  const config = useRuntimeConfig()
  const baseUrl = config.public.mobile_site_host || 'https://m.smartdeer.work'
  const imageUrl = `${baseUrl}/images/tg_banner.png`
  const pageUrl = `${baseUrl}/ja`

  useHead({
    htmlAttrs: { lang: 'ja' },
    title: 'SmartDeer - グローバル採用・海外雇用のワンストップHRソリューション',
    meta: [
      { name: 'description', content: 'SmartDeerは世界150カ国以上でグローバル採用、海外雇用、給与管理などの包括的な人事サービスを提供する業界をリードするHRプラットフォームです。EOR、PEO、コンプライアンスサービスで企業の国際展開を支援。今すぐお問い合わせください！' },
      { name: 'keywords', content: 'グローバル採用,海外雇用,人事アウトソーシング,EOR,国際給与,海外人材,SmartDeer,名義雇主,業務委託' },
      { name: 'robots', content: 'index, follow' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      { name: 'author', content: 'SmartDeer' },

      // Open Graph標签
      { property: 'og:title', content: 'SmartDeer - グローバル採用・海外雇用のワンストップHRソリューション' },
      { property: 'og:description', content: 'SmartDeerは世界150カ国以上でグローバル採用、海外雇用、給与管理などの包括的な人事サービスを提供し、企業の海外展開を支援します。' },
      { property: 'og:type', content: 'website' },
      { property: 'og:url', content: pageUrl },
      { property: 'og:site_name', content: 'SmartDeer' },
      { property: 'og:image', content: imageUrl },
      { property: 'og:image:width', content: '1390' },
      { property: 'og:image:height', content: '781' },
      { property: 'og:image:type', content: 'image/png' },
      { property: 'og:image:alt', content: 'SmartDeer - グローバル人事サービスプラットフォーム' },
      { property: 'og:locale', content: 'ja_JP' },

      // Twitter Card標签
      { name: 'twitter:card', content: 'summary_large_image' },
      { name: 'twitter:title', content: 'SmartDeer - グローバル採用・海外雇用のワンストップHRソリューション' },
      { name: 'twitter:description', content: 'SmartDeerは世界150カ国以上でグローバル採用、海外雇用、給与管理などの包括的な人事サービスを提供し、企業の海外展開を支援します。' },
      { name: 'twitter:image', content: imageUrl },
      { name: 'twitter:image:alt', content: 'SmartDeer - グローバル人事サービスプラットフォーム' }
    ],
    link: [
      // Canonical URL
      { rel: 'canonical', href: pageUrl },

      // Hreflang標签
      { rel: 'alternate', hreflang: 'zh', href: `${baseUrl}/zh` },
      { rel: 'alternate', hreflang: 'en', href: `${baseUrl}/en` },
      { rel: 'alternate', hreflang: 'ja', href: `${baseUrl}/ja` },
      { rel: 'alternate', hreflang: 'x-default', href: baseUrl }
    ],
    script: [
      // 结构化数据 - WebSite + Organization
      {
        type: 'application/ld+json',
        children: JSON.stringify({
          "@context": "https://schema.org",
          "@graph": [
            {
              "@type": "WebSite",
              "@id": `${baseUrl}/#website`,
              "url": baseUrl,
              "name": "SmartDeer",
              "description": "グローバル採用・海外雇用のワンストップHRソリューション",
              "publisher": {
                "@id": `${baseUrl}/#organization`
              },
              "potentialAction": [
                {
                  "@type": "SearchAction",
                  "target": {
                    "@type": "EntryPoint",
                    "urlTemplate": `${baseUrl}/search?q={search_term_string}`
                  },
                  "query-input": "required name=search_term_string"
                }
              ],
              "inLanguage": "ja-JP"
            },
            {
              "@type": "Organization",
              "@id": `${baseUrl}/#organization`,
              "name": "SmartDeer",
              "url": baseUrl,
              "logo": {
                "@type": "ImageObject",
                "url": `${baseUrl}/images/logo.png`
              },
              "description": "SmartDeerは世界をリードするグローバル人事サービスプラットフォームで、採用、雇用、給与管理などのワンストップサービスを提供",
              "sameAs": [
                "https://www.linkedin.com/company/smartdeer-global/",
                "https://twitter.com/smartdeer"
              ],
              "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "+852-3008-5033",
                "contactType": "customer service",
                "availableLanguage": ["Chinese", "English", "Japanese"]
              }
            }
          ]
        })
      }
    ]
  })
  
  const scrollItems = []
  const lifecycleItems = ref([0, 1, 2, 3, 4, 5, 6])
  const count = lifecycleItems.value.length
  
  setInterval(() => {
    lifecycleItems.value = lifecycleItems.value.map((item) => {
      return (item + 1) % count
    })
  }, 2200)
  
  const status = reactive({
    showForm: false,
    showConsultantCode: false
  })
  
  const form = reactive({
    name: '',
    company: '',
    service: '',
    countryCode: '+86',
    mobile: '',
    email: '',
    extra: ''
  })
  
  const serviceOptions = ref([
    { label: 'Recruitment' },
    { label: 'EOR' },
    { label: 'Contractor' },
    { label: 'PEO' },
    { label: '其他' }
  ])
  
  const countryCode = ref(phoneArea)
  
  const solutionExpandStatus = ref({
    s1: false,
    s2: false,
    s3: false,
    s4: false,
    s5: false,
    s6: false
  })
  
  const defaultHeight = '315px'
  const fullHeight = '2000px'
  
  function getSolutionDescStyle(key) {
    return {
      maxHeight: solutionExpandStatus.value[key] ? fullHeight : defaultHeight,
      overflow: 'hidden',
      transition: 'max-height 0.8s ease-out'
    }
  }
  
  let beforeExpandTop = null
  function toggle(key) {
    if (!solutionExpandStatus.value[key]) {
      beforeExpandTop = window.scrollY
    } else {
      smoothScrollTo(700, beforeExpandTop)
      beforeExpandTop = null
    }
    solutionExpandStatus.value[key] = !solutionExpandStatus.value[key];
  }
  
  function smoothScrollTo(duration, target) {
    console.log(target)
    const start = window.scrollY
    const startTime = performance.now()
  
    function scrollStep (currentTime) {
      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)
      const newScrollY = start + (target - start) * progress
      window.scrollTo(0, newScrollY)
      if (progress < 1) {
        requestAnimationFrame(scrollStep)
      }
    }
    requestAnimationFrame(scrollStep)
  }
  
  // 自定义指令
  const vScrollShow = {
    mounted: (el, bindings) => {
      const delayOffset = bindings.value.delayOffset || 0
      scrollItems.push({ offsetTop: el.offsetTop + delayOffset, el: el })
    }
  }
  
  let timer = null
  function scroll(e) {
    if (timer) return
    timer = setTimeout(() => {
      const offset = window.scrollY + window.innerHeight
      scrollItems.forEach((item, index) => {
        if (item.offsetTop < offset) {
          item.el.setAttribute('show', true)
          scrollItems.splice(index, 1)
        }
      })
      timer = null
    }, 30)
  }
  
  const cozeWebSDK = ref(null)
  
  function toggleChat() {
    console.log(cozeWebSDK.value)
    if (cozeWebSDK.value) {
      cozeWebSDK.value.showChatBot()
    }
  }
  
  onMounted(() => {
    window.addEventListener('scroll', scroll)
  
    const curScroll = getQueryString('scroll')
    if (curScroll) {
      scrollTo('#' + curScroll)
    }
  
    const player = videojs('video-player', {
      controls: true
    });
  
    cozeWebSDK.value = new CozeWebSDK.WebChatClient({
      config: {
        botId: '7439335660751716386'
      },
      ui: {
        base: {
          icon: 'https://static.smartdeer.com/bot_logo.png',
          layout: 'mobile',
          zIndex: 1000
        },
        chatBot: {
          title: '顾问杰哥',
          uploadable: false,
        },
        asstBtn: {
          isNeed: false
        },
        footer: {
          isShow: true,
          expressionText: 'Powered by SmartDeer.'
        }
      }
    })
  })
  
  onBeforeUnmount(() => {
    window.removeEventListener('scroll', scroll)
  })
  
  function scrollTo(tag) {
    const ele = window.document.querySelector(tag)
    if (ele) window.scrollTo({
      top: ele.offsetTop,
      behavior: 'smooth'
    })
  }
  
  function submitSuccess() {
    status.showForm = false
    ElMessage.success('Submit success!')
  }
  
  function switchLang(lang) {
    langTool.swithLang(lang)
  }
  
  </script>
  
  <style lang="scss" scoped>
  @import '@/assets/styles/en.scss';
  
  .index-page {
    --max-width: 680px;
    min-width: 375px;
    // font-family: Silka, sans-serif;
    font-family: Helvetica;
  
  
    section {
      .section-title {
        text-align: center;
        line-height: 1;
        margin-bottom: 20px;
  
        h2 {
          font-size: 20px;
          margin-bottom: 6px;
        }
  
        p {
          font-size: 10px;
          font-size: 10px;
          color: #999999;
        }
      }
    }
  }
  
  .anchor {
    position: fixed;
    right: 20px;
    bottom: 186px;
    z-index: 99;
  
    .consultant {
      width: 73px;
      height: 81px;
      cursor: pointer;
    }
    .consultant-code {
      width: 236px;
      height: 321px;
      position: fixed;
      right: 83px;
      bottom: 169px;
  
      .close {
        width: 20px;
        height: 20px;
        cursor: pointer;
        position: absolute;
        top: 27px;
        right: 35px;
        // border: 1px solid #000;
      }
    }
  
    figure {
      width: 100%;
      height: 100%;
  
      img{
        display: block;
        width: 100%;
        height: 100%;
      }
    }
  }
  
  header {
    display: block;
    position: relative;
    background-color: #FFF3E6;
    // height: 500px;
    width: 100%;
    overflow: hidden;
  
    .header-background {
      height: 100%;
      position: absolute;
      width: 100%;
      z-index: 0;
      top: 0;
  
      .decoration {
        transform: rotate(-45deg);
        transform-origin: 1000px 1000px;
        background: #FF8600;
        border-radius: 100px;
        height: 2000px;
        width: 2000px;
        top: 50%;
        margin-top: -876px;
        left: 50%;
        margin-left: 372px;
        position: absolute;
      }
    }
  
    .header-content {
      position: relative;
      z-index: 1;
      width: 100%;
      max-width: var(--max-width);
      margin: 0 auto;
      padding: 0 20px 40px 20px;
      box-sizing: border-box;
  
      .logo-row {
        padding: 16px 0 13px 0;
        display: flex;
        justify-content: space-between;
        align-items:flex-end;
  
        .logo {
          img {
            width: 110px;
          }
        }
  
        .extra {
          display: flex;
          font-size: 12px;
  
          .language {
            margin-left: 24px;
            display: flex;
            align-items: center;
            .text {
              font-size: 12px;
            }
          }
        }
      }
  
      .nav {
        background: rgba(0, 0, 0, 0.84);
        border: 0.5px solid rgba(0, 0, 0, 0.28);
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.34);
        border-radius: 6px;
        margin: 0 -8px;
        display: flex;
        justify-content: center;
  
        .nav-item {
          color: #fff;
          font-size: 12px;
          width: 25%;
          display: flex;
          justify-content: center;
          padding: 16px 0;
          box-sizing: border-box;
        }
      }
  
      .slogon {
        font-size: 20px;
        font-weight: bold;
        color: #000000;
        line-height: 24px;
        margin-top: 33px;
      }
  
      h1.site-title {
        font-size: 14px;
        font-weight: bold;
        color: #000000;
        line-height: 20px;
        margin-top: 16px;
      }
  
      .desc {
        margin-top: 25px;
        font-size: 12px;
        font-weight: 300;
        color: #000000;
        line-height: 17px;
  
        .desc-text {
          width: 172px;
        }
  
        .image {
          width: 187px;
          position: absolute;
          right: -9px;
          bottom: 16px;
  
          figure{
            width: 187px;
          }
  
          img {
            width: 100%;
            display: block;
          }
        }
      }
    }
  }
  
  section.customer {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0px 20px;
    box-sizing: border-box;
    margin-top: 50px;
  
    :deep(.el-carousel__arrow) {
      display: none !important;
    }
  
    .customer-list {
      display: flex;
      flex-wrap: wrap;
  
      .customer-item {
        width: 25%;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
  
        img {
          width: 75px;
          height: 28px;
        }
      }
    }
  }
  
  section.service {
    max-width: var(--max-width);
    margin: 56px auto 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
  
    .service-item {
      margin-bottom: 30px;
  
      .figure-area {
        margin-bottom: 20px;
  
        figure {
          display: block;
  
          img, #video-player {
            width: 100%;
          }
  
          #video-player {
            width: 100%;
            height: 212px;
            background: transparent;
  
            video {
              border-radius: 20px;
              overflow: hidden;
            }
          }
        }
      }
  
      .service-content {
        .service-title {
          line-height: 1;
          margin-bottom: 10px;
          color: #333333;
  
          h3 {
            font-size: 20px;
            margin-bottom: 8px;
          }
  
          p {
            font-size: 12px;
          }
        }
  
        .service-desc {
          font-size: 11px;
          line-height: 16px;
          color: #666666;
  
          p {
            margin-bottom: 10px;
            text-indent: 1em;
            position: relative;
  
            &::before{
              content: '';
              width: 3px;
              height: 3px;
              background: #FF7F00;
              position: absolute;
              left: 0;
              top: 7px;
            }
  
            &:last-child{
              margin: 0;
            }
          }
          .service-contact-button {
            background: transparent;
            line-height: 24px;
            font-size: 12px;
            border-radius: 20px;
            padding: 0 15px;
            border: 1px #000 solid;
            margin-top: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            color: #666;
          }
        }
      }
    }
  }
  
  section.advantage {
    max-width: var(--max-width);
    margin: 0 auto;
    padding-left: 20px;
    box-sizing: border-box;
  
  
    .advantage-list {
      display: flex;
      white-space: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
  
      .advantage-item {
        min-width: 40%;
        box-sizing: border-box;
        margin-right: 8px;
        background-color: #FEEFDF;
        padding: 14px 0;
        border-radius: 7px;
        text-align: center;
  
        figure.advantage-icon-area {
          width: 100%;
  
          img {
            display: block;
            width: 26px;
            margin: 0 auto;
          }
        }
  
        .advantage-title {
          margin-top: 6px;
          font-weight: bold;
          white-space: normal;
        }
  
        .advantage-content {
          display: block;
          overflow: hidden;
          padding: 0 8px;
          font-size: 11px;
          font-weight: 500;
          color: #333333;
          line-height: 14px;
          margin-top: 6px;
          white-space: normal;
          word-wrap: break-word;
          word-break:normal;
        }
      }
    }
  }
  
  section.lifecycle {
    margin-top: 48px;
  
    .lifecycle-list {
      overflow: hidden;
  
      .lifecycle-list-container {
        position: relative;
        display: flex;
        width: 100%;
        overflow: hidden;
        padding-bottom: 20px;
        position: relative;
        height: 128px;
        max-width: 680px;
        margin: 0 auto;
  
        .lifecycle-item {
          width: 128px;
          flex: 0 0 auto;
          transition: all .4s ease-in-out;
          position: absolute;
  
          // --------
          &[data-nth="0"] {
            opacity: 0;
            transform-origin: center;
            transform: scale(0.2);
            left: 105%;
            margin-left: -64px;
          }
  
          &[data-nth="6"] {
            opacity: 0;
            transform-origin: center;
            transform: scale(0.2);
            left: -10%;
            margin-left: -64px;
          }
  
          // --------
          &[data-nth="5"] {
            opacity: 0.2;
            transform-origin: center;
            transform: scale(0.5);
            left: 5%;
            margin-left: -64px;
          }
  
          &[data-nth="1"] {
            opacity: 0.2;
            transform-origin: center;
            transform: scale(0.5);
            left: 95%;
            margin-left: -64px;
          }
  
          // --------
          &[data-nth="4"] {
            opacity: 0.7;
            transform-origin: center;
            transform: scale(0.7);
            left: 25%;
            margin-left: -64px;
          }
  
          &[data-nth="2"] {
            opacity: 0.7;
            transform-origin: center;
            transform: scale(0.7);
            left: 75%;
            margin-left: -64px;
          }
  
          // ----
          &[data-nth="3"] {
            opacity: 1;
            transform-origin: center;
            transform: scale(1);
            left: 50%;
            margin-left: -64px;
          }
  
          figure {
            img {
              width: 100%;
            }
          }
  
          .title {
            text-align: center;
          }
        }
      }
    }
  }
  
  section.process {
    margin: 48px 0 96px 0;
  
    .process-list {
      .process-item {
        margin-top: 44px;
  
        &:nth-child(2n+1) {
          .process-head {
            // width: 100%;
            background-image: linear-gradient(90deg, #FFFFFF 15%, #FFF2E3 100%);
          }
  
        }
  
        &:nth-child(2n) {
          .process-head {
            // width: 100%;
            background-image: linear-gradient(270deg, #FFFFFF 15%, #FFF2E3 100%);
          }
        }
  
        .process-head {
          margin-bottom: 20px;
  
          .process-head-wrapper {
            max-width: var(--max-width);
            margin: 0 auto;
            padding: 0 20px;
            box-sizing: border-box;
            display: flex;
  
            figure {
              width: 80%;
              position: relative;
              top: -24px;
  
              img {
                width: 100%;
                filter: drop-shadow(0 1px 5px RGBA(0, 0, 0, .18))
              }
            }
  
            .num {
              width: 20%;
              flex: 1 1 auto;
              text-align: center;
              font-size: 24px;
              color: #333333;
              letter-spacing: 0;
              font-weight: bold;
              position: relative;
              top: 16px;
            }
          }
        }
  
        .process-content {
          max-width: var(--max-width);
          margin: 0 auto;
          padding: 0 20px;
          box-sizing: border-box;
  
          .title {
            font-size: 20px;
            color: #333333;
            font-weight: 500;
            margin-bottom: 10px;
            line-height: 22px;
          }
  
          .desc {
            font-size: 11px;
            font-weight: 300;
            line-height: 15px;
          }
        }
      }
    }
  }
  // 服务模式模块
  section.solution {
    max-width: var(--max-width);
    padding: 0;
    box-sizing: border-box;
    margin: 53px auto 0 auto;
  
    .section-title {
      margin-bottom: 0;
    }
  
    .solution-list {
      margin: 0 auto;
  
      .case1-bg {
        background: linear-gradient(to bottom, rgba(255, 255, 255, 0.4), rgba(239, 223, 207, 0.4));
        .solution-content.collapsed {
          &:after {
            content: '';
            position: absolute;
            bottom: 40px;
            left: 0;
            width: 100%;
            height: 50px;
            background-image: linear-gradient(-180deg,rgba(255,255,255,0) 0%,#F8F3EE 100%);
            pointer-events: none;
          }
        }
      }
      .case2-bg {
        background: linear-gradient(to bottom, rgba(209, 237, 217, 0), rgba(209, 237, 217, 0.4));
        .solution-content.collapsed {
          &:after {
            content: '';
            position: absolute;
            bottom: 40px;
            left: 0;
            width: 100%;
            height: 50px;
            background-image: linear-gradient(-180deg,rgba(255,255,255,0) 0%,#F0F8F1 100%);
            pointer-events: none;
          }
        }
      }
      .case3-bg {
        background: linear-gradient(to bottom, rgba(149, 201, 246, 0), rgba(149, 201, 246, 0.4));
        .solution-content.collapsed {
          &:after {
            content: '';
            position: absolute;
            bottom: 40px;
            left: 0;
            width: 100%;
            height: 50px;
            background-image: linear-gradient(-180deg,rgba(255,255,255,0) 0%,#DDECFB 100%);
            pointer-events: none;
          }
        }
      }
      .case4-bg {
        background: linear-gradient(to bottom, rgba(251, 235, 186, 0.04), rgba(251, 235, 186, 0.4));
        .solution-content.collapsed {
          &:after {
            content: '';
            position: absolute;
            bottom: 40px;
            left: 0;
            width: 100%;
            height: 50px;
            background-image: linear-gradient(-180deg,rgba(255,255,255,0) 0%,#FDF8E8 100%);
            pointer-events: none;
          }
        }
      }
      .case5-bg {
        background: linear-gradient(to bottom, rgba(209, 237, 217, 0), rgba(209, 237, 217, 0.4));
        .solution-content.collapsed {
          &:after {
            content: '';
            position: absolute;
            bottom: 40px;
            left: 0;
            width: 100%;
            height: 50px;
            background-image: linear-gradient(-180deg,rgba(255,255,255,0) 0%,#F1F8F2 100%);
            pointer-events: none;
          }
        }
      }
      .case6-bg {
        background: linear-gradient(to bottom, rgba(149, 201, 246, 0), rgba(149, 201, 246, 0.4));
        .solution-content.collapsed {
          &:after {
            content: '';
            position: absolute;
            bottom: 40px;
            left: 0;
            width: 100%;
            height: 50px;
            background-image: linear-gradient(-180deg,rgba(255,255,255,0) 0%,#DDECFB 100%);
            pointer-events: none;
          }
        }
      }
  
      .solution-item {
        padding-top: 30px;
        padding-bottom: 30px;
  
        .solution-wrapper {
          padding: 0 20px;
          margin: 0 auto;
  
          .solution-figure {
            position: relative;
            transition: all .5s;
  
            figure {
              img {
                width: 100%;
              }
            }
          }
  
          .solution-content {
            position: relative;
            opacity: 1;
            width: 100%;
            flex: 0 0 auto;
            box-sizing: border-box;
            transition: all .5s;
  
            .solution-title {
              color: #333333;
  
              h3 {
                font-size: 20px;
                font-weight: 500;
                font-family: PingFangSC-Medium, PingFang SC;
                line-height: 28px;
                color: #333333;
                margin: 20px 0 0 0;
              }
  
              p {
                font-size: 18px;
                font-family: DIN-Regular, DIN;
                font-weight: 400;
                color: #999;
                line-height: 26px;
                margin: 0;
              }
            }
  
            .solution-desc {
              font-size: 16px;
              color: #333;
              line-height: 27px;
              h4 {
                margin: 10px 0 10px 0;
                font-weight: bold;
              }
              ol {
                position: relative;
                padding-left: 24px;
                margin: 0
              }
              ol li {
                list-style: decimal;
                position: relative;
              }
              ul {
                position: relative;
                padding-left: 20px;
                margin: 0;
              }
              ul li {
                position: relative;
              }
  
              li>ul {
                padding-left: 20px;
              }
              li>ul li{
                list-style: circle;
              }
              p{
                position: relative;
                margin: 0;
              }
            }
  
            .solution-expand {
              margin-top: 20px;
              .solution-toggle {
                background: transparent;
                border: 1px #000 solid;
                padding: 0 20px;
                line-height: 32px;
                border-radius: 15px;
                cursor: pointer;
                display: flex;
                align-items: center;
                color: #2D2D2D;
              }
              .inline-arrow {
                width: 16px;
                height: 16px;
                margin-left: 5px;
              }
            }
          }
        }
      }
    }
  }
  .inline-arrow {
    width: 16px;
    height: 16px;
    margin-left: 5px;
  }
  .bot-container {
    z-index: 1000;
    cursor: pointer;
    display: flex;
    position: fixed;
    bottom: 104px;
    right: 20px;
    transition: transform 0.3s ease;
    &:hover {
      transform: scale(1.16);
    }
    img {
      width: 70px;
      height: 80px;
    }
  }
  .go-top-container {
    cursor: pointer;
    display: flex;
    position: fixed;
    bottom: 30px;
    right: 30px;
  
    img {
      width: 55px;
      height: 55px;
    }
  }
  </style>