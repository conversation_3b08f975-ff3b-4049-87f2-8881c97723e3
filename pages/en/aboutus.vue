<template lang="pug">
mixin header-nav
  section.header-nav
    NuxtLink(to="/")
      figure.logo
        img(src="~/assets/images/aboutus/sd_logo.png")
    .extra
      .contact-us(@click="()=>{status.showForm = true}")
        .text Contact Us

      //- 吃瓜就要付出代价，否则影响页面跳转
      //- https://github.com/element-plus/element-plus/pull/9731
      client-only
        el-dropdown.language-selector
          .text(style="color: #000;") 中 / EN
          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(@click="switchLang('zh')") 中文
              el-dropdown-item(@click="switchLang('en')") English

mixin company-profile 
  section.company-profile
    .section-title
      h2.title Your Global HR Solutions Partner

    .profile-content
      figure
        img(src="~/assets/images/aboutus/company-profile.webp")
      h4 Company Background
      p SmartDeer is a one-stop "HR service and SaaS" platform dedicated to global recruitment and employment solutions. Incubated by Trustbridge Partners, with investments led by Welight Capital, WeWork, and Hash Global, SmartDeer helps businesses transcend geographical boundaries, swiftly recruit global talent, and manage the entire employment lifecycle, including onboarding, offboarding, payroll, taxes, and benefits. Our platform ensures compliance and efficiency at every step, simplifying global team management.
      p SmartDeer has achieved ISO 27001 certification, ensuring comprehensive protection of customers' data and privacy, and delivering a trusted and reliable service.
      h4 Global Influence
      p SmartDeer is dual-headquartered in Hong Kong and Singapore, providing us with a strategic global advantage while ensuring compliance with local data storage and processing regulations. In addition to subsidiaries and branches in regions like the United States, the United Kingdom, the UAE, Saudi Arabia, Australia, Japan, South Korea, Thailand, Malaysia, Indonesia, the Philippines, Vietnam, Mainland China，Mexico and Brazil.Our services, delivered through our self-operated network and partners, span over 150 countries and regions worldwide.
      h4 Our Team
      p SmartDeer has more than 150 employees across over a dozen countries, offering multilingual services, with a focus on Chinese and English, and strong capabilities in various local languages to cater to regional needs. Our team’s deep expertise in local laws and regulations allows us to provide professional, localized services with global support.
      h4 Service Scope
      p We provide comprehensive HR solutions for talent recruitment, global compliant employment, visa processing, payroll management, benefits, and tax administration. SmartDeer’s robust Global HR SaaS system enables businesses to easily manage the complexities of global HR operations, streamlining processes and mitigating compliance risks. Our platform allows companies to recruit talent globally, manage onboarding and offboarding, track vacations, and handle payroll and taxes seamlessly, helping businesses succeed globally.

mixin profession
  section.profession
    .profession-list
      .profession-item
        figure 
          img(src="~/assets/images/aboutus/earth.svg")
        .content
          .title Global Collaboration
          .desc Our teams are located in the United States, the United Kingdom, the United Arab Emirates, Saudi Arabia, Australia, Singapore, Japan, South Korea, Thailand, Malaysia, Indonesia, the Philippines, Vietnam, Hong Kong and other places.

      .profession-item
        figure 
          img(src="~/assets/images/aboutus/team.svg")
        .content
          .title Professional Team
          .desc Our members come from all over the world, with over 10 years of HR experience in top companies.

      .profession-item
        figure 
          img(src="~/assets/images/aboutus/cover.svg")
        .content
          .title Full Coverage of Services
          .desc Our services include global talent recruitment, EOR, global contractors, global human service professional outsourcing and consulting.

mixin global-office
  section.global-office
    .section-title
      h2.title Global Office

    .office-list
      .office-item
        figure 
          img(src="~/assets/images/aboutus/hongkong.webp")
        .content
          .title Hong Kong
          .location Address: Room 705-706, 7/F., China Insurance Group Building, No. 141 Des Voeux Road Central, Central, Hong Kong
      .office-item
        figure
          img(src="~/assets/images/aboutus/singapore.webp")
        .content
          .title Singapore
          .location Address: 3 Fraser Street, #5-25 Duo Tower, Singapore (189352)
      .office-item
        figure
          img(src="~/assets/images/aboutus/california.jpeg")
        .content
          .title United States
          .location Address：15025 PROCTOR AVAVE CITY OF INDUSTRY, Y, CA CA 91746
      .office-item
        figure
          img(src="~/assets/images/aboutus/uk.jpeg")
        .content
          .title United Kingdom
          .location Address：69 Aberdeen Avenue, Cambridge, England, CB2 8DL
      .office-item
        figure
          img(src="~/assets/images/aboutus/australia.jpeg")
        .content
          .title Australia
          .location Address：135 KING STREET, SYDNEY, NSW 2000
      .office-item
        figure
          img(src="~/assets/images/aboutus/aue.jpeg")
        .content
          .title The United Arab Emirates
          .location Address：Office328,BlockB,Business Village, Deira, Dubai, UAE
      .office-item
        figure
          img(src="~/assets/images/aboutus/japan.jpeg")
        .content
          .title Japan
          .location Address：神奈川県横浜市中区山下町98GSハイム山下町4階403号室
      .office-item
        figure
          img(src="~/assets/images/aboutus/korea.jpeg")
        .content
          .title South Korea
          .location Address：서울특별시 중랑구 동일로825，2층 205호 (중화동)
      .office-item
        figure
          img(src="~/assets/images/aboutus/thailand.jpeg")
        .content
          .title Thailand
          .location Address：11 / 152-153 Room No. GDC 101, 1st Floor, Moo 5, Kookong District, Lam Luka District, Pathum Thani Province
      .office-item
        figure
          img(src="~/assets/images/aboutus/malaysia.jpeg")
        .content
          .title Malaysia
          .location Address：332F-­2, HARMONY SQUARE, JALAN PERAK 11600 JELUTONG PULAU PINANG MALAYSIA
      .office-item
        figure
          img(src="~/assets/images/aboutus/indonesia.jpeg")
        .content
          .title Indonesia
          .location Address：Gedung Wirausaha Lantai 1 Unit 104, Jalan HR Rasuna Said Kav. C-5,Desa/Kelurahan Karet, Kec. Setiabudi, Kota Adm. Jakarta Selatan, ProvinsiDKI Jakarta
      .office-item
        figure
          img(src="~/assets/images/aboutus/philippines.jpeg")
        .content
          .title The Philippines
          .location Address：UNIT 25D 2ND FLOOR ZETA II BLDG.191 SALCEDO ST., SAN LORENZO,CITY OF MAKATI,FOURTH DIRECT, NATIONAL CAPITAL REGION (NCR), 1223
      .office-item
        figure
          img(src="~/assets/images/aboutus/vietnam.jpeg")
        .content
          .title Vietnam
          .location Address：Room(s):28 ,Level 14, Saigon Centre Tower 1, No. 65 Le Loi Street, Ben Nghe Ward, District 1, Ho Chi Minh City,Vietnam
      .office-item
        figure
          img(src="~/assets/images/aboutus/beijing.webp")
        .content
          .title BeiJing
          .location Address: 4F, Wangfu International Center wework,Wangfujing, Dongcheng District, Beijing
      .office-item
        figure
          img(src="~/assets/images/aboutus/shanghai.webp")
        .content
          .title ShangHai
          .location Address: Floor 12, Huirong Building, No. 535, Caoyang Road, Putuo District, Shanghai Chengdu: 6F-K0
      .office-item
        figure
          img(src="~/assets/images/aboutus/shenzhen.webp")
        .content
          .title ShenZhen
          .location Address: 0701-D021, Port Building, Ocean Freight Center, No. 59, Linhai Avenue, Nanshan Street, Shenzhen-Hong Kong Modern Service Industry Cooperation Zone in Qianhai, Shenzhen
      .office-item
        figure
          img(src="~/assets/images/aboutus/hangzhou.webp")
        .content
          .title HangZhou
          .location Address: Room 205, Building 2, No. 8-1, Longquan Road, Qianjie Street, Yuhang District, Hangzhou City, Zhejiang Province
      .office-item
        figure
          img(src="~/assets/images/aboutus/chengdu.webp")
        .content
          .title ChengDu
          .location Address: 6F-K0063, Lei Shing Hong Plaza, No. 5 Hangtian Road, Chenghua District, Chengdu City, Sichuan Province

mixin contact-us
  .contact-form
    //- 吃瓜就要付出代价，否则影响页面跳转
    //- https://github.com/element-plus/element-plus/pull/9731
    client-only
      el-dialog(v-model="status.showForm" title="" :width="354")
        contact-us-form(@submit="submitSuccess" lang="en" :title="pageTitle")

.page-about-us
  .header
    +header-nav
    +company-profile

  +profession
  +global-office

  +contact-us

  SiteFooter(lang="en" @contact-us="()=>{ status.showForm = true }")

</template>

<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {getQueryString} from "assets/utils";
definePageMeta({ layout: 'basic' })

// 获取运行时配置
const config = useRuntimeConfig()
const baseUrl = config.public.mobile_site_host || 'https://m.smartdeer.work'
const imageUrl = `${baseUrl}/images/aboutus_banner.png`
const pageUrl = `${baseUrl}/en/aboutus`

useHead({
  htmlAttrs: { lang: 'en-US' },
  title: 'About SmartDeer - Global HR Solutions Provider | Company',
  meta: [
    { name: 'description', content: 'Founded in 2019, SmartDeer is Asia\'s leading global HR platform headquartered in Hong Kong and Singapore. We provide professional recruitment, employment, and payroll services across 150+ countries with ISO 27001 certification. Your trusted partner for global expansion.' },
    { name: 'keywords', content: 'SmartDeer company,global HR provider,international human resources,EOR services,global recruitment platform,HR outsourcing,employment solutions' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { name: 'author', content: 'SmartDeer' },

    // Open Graph标签
    { property: 'og:title', content: 'About SmartDeer - Leading Global HR Solutions Provider | Company Profile' },
    { property: 'og:description', content: 'SmartDeer is a leading global HR service platform headquartered in Hong Kong and Singapore, providing recruitment, employment, and payroll services across 150+ countries. ISO 27001 certified for trusted global partnerships.' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: pageUrl },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:image', content: imageUrl },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:image:type', content: 'image/png' },
    { property: 'og:image:alt', content: 'SmartDeer Company Profile' },
    { property: 'og:locale', content: 'en_US' },

    // Twitter Card标签
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'About SmartDeer - Leading Global HR Solutions Provider | Company Profile' },
    { name: 'twitter:description', content: 'SmartDeer is a leading global HR service platform headquartered in Hong Kong and Singapore, providing recruitment, employment, and payroll services across 150+ countries. ISO 27001 certified for trusted global partnerships.' },
    { name: 'twitter:image', content: imageUrl },
    { name: 'twitter:image:alt', content: 'SmartDeer Company Profile' }
  ],
  link: [
    // Canonical URL
    { rel: 'canonical', href: pageUrl },

    // Hreflang标签
    { rel: 'alternate', hreflang: 'zh', href: `${baseUrl}/zh/aboutus` },
    { rel: 'alternate', hreflang: 'en', href: `${baseUrl}/en/aboutus` },
    { rel: 'alternate', hreflang: 'ja', href: `${baseUrl}/ja/aboutus` },
    { rel: 'alternate', hreflang: 'x-default', href: `${baseUrl}/aboutus` }
  ],
  script: [
    // 结构化数据 - Organization + AboutPage
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@graph": [
          {
            "@type": "AboutPage",
            "@id": `${pageUrl}#aboutpage`,
            "url": pageUrl,
            "name": "About SmartDeer",
            "description": "SmartDeer company profile page, learn about our global HR services",
            "mainEntity": {
              "@id": `${baseUrl}/#organization`
            },
            "inLanguage": "en-US"
          },
          {
            "@type": "Organization",
            "@id": `${baseUrl}/#organization`,
            "name": "SmartDeer",
            "url": baseUrl,
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/images/logo.png`
            },
            "description": "SmartDeer is a leading global HR service platform providing recruitment, employment, and payroll management solutions",
            "foundingDate": "2020",
            "numberOfEmployees": "150+",
            "address": [
              {
                "@type": "PostalAddress",
                "addressLocality": "Hong Kong",
                "addressCountry": "HK"
              },
              {
                "@type": "PostalAddress",
                "addressLocality": "Singapore",
                "addressCountry": "SG"
              }
            ],
            "contactPoint": {
              "@type": "ContactPoint",
              "telephone": "+852-3008-5033",
              "contactType": "customer service",
              "availableLanguage": ["Chinese", "English", "Japanese"]
            },
            "sameAs": [
              "https://www.linkedin.com/company/smartdeer-global/",
              "https://twitter.com/smartdeer"
            ],
            "hasCredential": {
              "@type": "EducationalOccupationalCredential",
              "credentialCategory": "certification",
              "name": "ISO 27001 Certification"
            }
          }
        ]
      })
    }
  ]
})

const status = reactive({
  showForm: false
})

function switchLang(lang) {
  langTool.swithLang(lang, '/aboutus')
}

function submitSuccess() {
  status.showForm = false
  ElMessage.success('submit success!')
}

onMounted(() => {
  const autoPromptContact = getQueryString('contact')
  if (autoPromptContact === 'true') {
    setTimeout(() => {
      status.showForm = true
    }, 1000);
  }
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/en.scss';

.page-about-us {
  font-family: Helvetica;

  min-width: 375px;
  section {
    .section-title {
      text-align: center;

      h2.title {
        font-size: 20px;
        line-height: 24px;
        display: inline-block;
        position: relative;

        &::before {
          content: '';
          display: block;
          position: absolute;
          left: 0;
          bottom: 1px;
          width: 100%;
          background-color: #FF8600;
          height: 4px;
          z-index: -1;
        }
      }
    }
  }

  .header {
    background-image: url("~/assets/images/aboutus/map-bg.png");
    background-position: center 8px;
    background-repeat: no-repeat;
    background-size: auto 420px;
  }
}

section.header-nav {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 11px;

  figure.logo {
    flex: 0 0 auto;

    img {
      width: 81px;
    }
  }

  .extra {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #000000;

    .language-selector {
      margin-left: 24px;

      .text {
        font-size: 12px;
      }
    }
  }
}

section.company-profile {
  margin: 0 auto;
  max-width: 420px;
  padding: 0 20px;
  box-sizing: border-box;
  margin-top: 47px;

  .profile-content {
    padding-bottom: 47px;
    margin-top: 22px;

    figure {
      margin: 0px -16px;
      img {
        width: 100%;
      }
    }

    h3 {
      margin-top: 17px;
      font-size: 28px;
      color: #333;
    }
    h4 {
      margin-top: 20px;
      margin-bottom: 15px;
      font-size: 24px;
      color: #333;
    }
    p {
      font-size: 16px;
      letter-spacing: 1px;
      line-height: 24px;
      color: #333;
    }
  }
}

section.profession {
  background-color: #F7F9FA;
  padding: 32px 0;
  box-sizing: border-box;

  .profession-list {
    padding: 0 20px;
    box-sizing: border-box;
    max-width: 420px;
    margin: 0 auto;

    .profession-item {
      box-sizing: border-box;
      margin-bottom: 32px;

      &:last-child{
        margin-bottom: 0;
      }

      figure {
        img {
          width: 40px;
          margin: 0 auto;
        }
      }

      .content {
        margin-top: 15px;

        .title {
          font-size: 18px;
          text-align: center;
          color: #333333;
          line-height: 22px;
        }

        .desc {
          font-size: 14px;
          font-weight: 300;
          line-height: 22px;
          margin-top: 11px;
        }
      }
    }
  }
}

section.global-office {
  margin: 0 auto;
  padding: 48px 20px 40px;
  box-sizing: border-box;
  max-width: 420px;

  .office-list {
    margin-top: 22px;
    .office-item {
      box-sizing: border-box;
      margin-bottom: 40px;
      &:last-child{
        margin-bottom: 0;
      }

      figure {
        margin-bottom: 24px;

        img {
          width: 100%;
        }
      }

      .content {
        margin-top: 21px;

        .title {
          font-size: 18px;
          color: #333333;
          margin-bottom: 10px;
          text-align: center;
          line-height: 22px;
        }

        .location, .contact{
          font-size: 14px;
          color: #333333;
          line-height: 21px;
        }
      }
    }
  }
}
</style>