<!--
 * @Author: sx <EMAIL>
 * @Date: 2023-01-13 13:03:36
 * @LastEditors: sx <EMAIL>
 * @LastEditTime: 2023-01-16 18:20:12
 * @FilePath: \bpo-website-mobile\components\site-header.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template lang="pug">
.site-header
  template(v-if="props.lang ==='zh'")
    .logo-row
      NuxtLink.logo(to="/")
        figure
          img(src="~/assets/images/index/sd_logo.png")
      .extra
        .language 
          client-only
            el-dropdown.language-selector(trigger="click")
              .text(style="color: #000;")
                img(src="~/assets/images/index/icon_earth.png")
              template(#dropdown)
                el-dropdown-menu
                  el-dropdown-item(@click="switchLang('zh')") 中文
                  el-dropdown-item(@click="switchLang('en')") English
                  el-dropdown-item(@click="switchLang('ja')") 日本語
        .contact-us(@click="()=>{$emit('contact-us')}") 联系我们
    .nav(:class="{'nav__radius': status.showContriesPopper}")
      .nav-slide
        .nav-item(@click="()=>{scrollTo('#service-recruitment')}") 全球人力资源服务
        .nav-item(@click="()=>{scrollTo('#service-solution')}") 行业案例
        .nav-item(@click="()=>{router.push('/zh/calculator')}") 雇主成本计算器
        .nav-item(@click.stop="status.showContriesPopper = !status.showContriesPopper") 资源库
          .icon
            ArrowDownBold(style="width: 9px; height: 9px;")
      .contries-popper(v-show="status.showContriesPopper" @click.stop="() => {}" ref="contriesPopper")
        .we-serve
          //- .we-serve__title COUNTRIES WE SERVE
          .we-serve__title 全球雇佣
          .we-serve__desc 掌握全球各国家地区人力资源法律法规，安全且高效地完成全球人才招聘与雇佣，为顺利开展全球化业务奠定坚实的基础。
        .country-list
          a.country-list__item(href="/zh/countries/" @click.stop="() => {}") 国家雇佣指南
          a.country-list__item(href="/zh/marketing/" @click.stop="() => {}") 媒体报道&市场活动
          a.country-list__item(href="/zh/articles/" @click.stop="() => {}") 文章

  template(v-if="props.lang ==='en'")
    .logo-row
      NuxtLink.logo(to="/")
        figure
          img(src="~/assets/images/index/sd_logo.png")
      .extra
        .language
          client-only
            el-dropdown.language-selector
              .text
                img(src="~/assets/images/index/icon_earth.png")
              template(#dropdown)
                el-dropdown-menu
                  el-dropdown-item(@click="switchLang('zh')") 中文
                  el-dropdown-item(@click="switchLang('en')") English
                  el-dropdown-item(@click="switchLang('ja')") 日本語
        .contact-us(@click="()=>{$emit('contact-us')}") Contact Us
    .nav.nav__en(:class="{'nav__radius': status.showContriesPopper}")
      .nav-item(@click="()=>{scrollTo('#service-recruitment')}") Global HR<br>Services
      .nav-item(@click="()=>{scrollTo('#service-solution')}") Success<br>Stories
      .nav-item(@click="()=>{router.push('/en/calculator')}") Employer Cost <br>Calculator
      .nav-item(@click.stop="status.showContriesPopper = !status.showContriesPopper") Resource<br>Library
        .icon
          ArrowDownBold(style="width: 9px; height: 9px;")

    .contries-popper.contries-popper__en(v-show="status.showContriesPopper" @click.stop="() => {}" ref="contriesPopper")
      .we-serve
        .we-serve__title COUNTRIES WE SERVE
        .we-serve__desc Confidently engage talent 150+ countries with expert insights into Iocal Iaws and regulations.
      .country-list
        a.country-list__item(href="/en/countries/" @click.stop="() => {}") Global Hiring Guide
        a.country-list__item(href="/en/marketing/" @click.stop="() => {}") Media & Marketing
        a.country-list__item(href="/en/articles/" @click.stop="() => {}") Articles

  template(v-if="props.lang ==='ja'")
    .logo-row
      NuxtLink.logo(to="/")
        figure
          img(src="~/assets/images/index/sd_logo.png")
      .extra
        .language
          client-only
            el-dropdown.language-selector
              .text
                img(src="~/assets/images/index/icon_earth.png")
              template(#dropdown)
                el-dropdown-menu
                  el-dropdown-item(@click="switchLang('zh')") 中文
                  el-dropdown-item(@click="switchLang('en')") English
                  el-dropdown-item(@click="switchLang('ja')") 日本語
    .nav.nav__en(:class="{'nav__radius': status.showContriesPopper}")
      .nav-item(@click="()=>{scrollTo('#service-recruitment')}") グローバル人材<br>サービス
      .nav-item(@click="()=>{scrollTo('#service-solution')}") 成功<br>ストーリー
      .nav-item(@click="()=>{router.push('/ja/calculator')}") 雇主コスト<br>計算機
      .nav-item(@click.stop="status.showContriesPopper = !status.showContriesPopper") リソース<br>ライブラリ
        .icon
          ArrowDownBold(style="width: 9px; height: 9px;")
    .contries-popper.contries-popper__en(v-show="status.showContriesPopper" @click.stop="() => {}" ref="contriesPopper")
      .we-serve
        .we-serve__title 対応国一覧
        .we-serve__desc 専門的な知見をもとに、150か国以上での現地雇用と法規制対応をサポートします。
      .country-list
        a.country-list__item(href="/ja/countries/" @click.stop="() => {}") グローバル採用ガイド
        a.country-list__item(href="/ja/marketing/" @click.stop="() => {}") メディア・マーケティング活動
        a.country-list__item(href="/ja/articles/" @click.stop="() => {}") 文章記事・コラム

</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import { ArrowDownBold } from '@element-plus/icons-vue'
import {useSessionStorage} from "@vueuse/core";
import { useRouter } from 'vue-router'
const router = useRouter()
const props = defineProps({
  lang: {
    type: String,
    default: ''
  },
  source: {
    type: String,
    default: ''
  }
})

const status = reactive({
  showContriesPopper: false
})
const contriesPopper = ref()
const USER_REF_KEY = 'USER_REF'

onMounted(() => {
  document.addEventListener('click', (e)=>{
    if (!contriesPopper.value.contains(e.target)) {
      status.showContriesPopper = false
    }
  })
  const userRef = useSessionStorage(USER_REF_KEY, '')
  if (!userRef.value && document.referrer !== '') {
    userRef.value = document.referrer
  }
})

function switchLang(lang) {
  const pathName = window.location.pathname
  const newPathName = pathName.substring(3)

  langTool.swithLang(lang, newPathName)
}

function scrollTo(tag) {
  if (props.source === 'home') {
    const ele = window.document.querySelector(tag)
    if (ele) window.scrollTo({
      top: ele.offsetTop,
      behavior: 'smooth'
    })
  } else {
    langTool.swithLang(props.lang as any, `/?scroll=${tag.substring(1)}`)
  }
}
</script>
<style lang="scss" scoped>
.site-header {
  padding: 0 20px;
  .logo-row {
    padding: 16px 0 13px 0;
    display: flex;
    justify-content: space-between;
    align-items:flex-end;

    .logo {
      img {
        width: 110px;
      }
    }

    .extra {
      display: flex;
      font-size: 12px;

      .contact-us {
        line-height: 26px;
        border-radius: 20px;
        background: #2D2D2D;
        color: #FFF;
        padding: 0 10px;
      }

      .language {
        margin-right: 10px;
        display: flex;
        align-items: center;
        .text {
          font-size: 12px;
          img {
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }

  .nav {
    background: rgba(0, 0, 0, 0.84);
    // border: 0.5px solid rgba(0, 0, 0, 0.28);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.34);
    border-radius: 6px;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    position: relative;
    padding: 16px;
    z-index: 99;

    &__radius {
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      box-shadow: none;
    }
    .icon {
      margin-left: 5px;
    }

    .nav-slide {
      width: 100%;
      display: flex;
      justify-content: space-between;
      position: relative;
      scrollbar-width: none; /* firefox */
      -ms-overflow-style: none; /* IE 10+ */
    }
    .nav-slide::-webkit-scrollbar {
      display: none; /* Chrome Safari */
    }


    .nav-item {
      color: #fff;
      font-size: 12px;
      display: flex;
      box-sizing: border-box;
      white-space:nowrap;

    }

    .countries {
      width: 90px;
      min-width: 90px;
      margin-right: 0px;
      position: relative;

      &:before {
        content: '';
        width: 2px;
        height: 23px;
        background: #5D5C5A;
        position: absolute;
        left: 0;
        top: 50%;
        margin-top: -11.5px;
      }
      .icon {
        // position: absolute;
       padding-top: 17px;
       margin-left: 3px;
      }
    }
  }
  .contries-popper {
    position: absolute;
    width: 100%;
    top: 48px;
    left: 0;
    background: rgba(0, 0, 0, 0.84);
    padding: 4px 16px 20px 16px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    color: #fff;
    z-index: 99999;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
    transition: all 0.2s;

    &__en {
      width: auto;
      top: 114px;
      left: 20px;
      right: 20px;
    }

    .we-serve {
      width: 137px;
      .we-serve__title {
        font-size: 12px;
        font-weight: bold;
        color: #FFFFFF;
        line-height: 14px;
      }

      .we-serve__desc  {
        margin-top: 9px;
        color: rgba(255,255,255,0.55);
        font-size: 11px;
        line-height: 20px;
      }
    }

    .country-list {
      .country-list__title {
        font-size: 12px;
        font-weight: bold;
        line-height: 19px;
        // margin-bottom: 8px;
        padding-left: 10px;
      }

      .country-list__item {
        font-weight: bold;
        width: 150px;
        height: 26px;
        line-height: 26px;
        font-size: 12px;
        padding-left: 10px;
        cursor: pointer;
        transition: all .2s;
        display: block;
        color: #fff;

        &:hover {
          background: #5C5C5C;
          color: #FE9111;
          transition: all .2s;
        }
      }
    }
  }
}
</style>
