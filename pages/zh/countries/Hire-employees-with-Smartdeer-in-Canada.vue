<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在加拿大雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在加拿大雇佣',
  ogDescription: '基础信息 首都：渥太华 时区：GMT-6 语言：法语、英语 货币代码：CAD 人力资源制度概况 雇佣合同 合同 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：渥太华 时区：GMT-6 语言：法语、英语 货币代码：CAD 人力资源制度概况 雇佣合同 合同 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在加拿大雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在加拿大雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/100ssa133tym44hww7CB2.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/R-C.png';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：渥太华</p><p>时区：GMT-6</p><p>语言：法语、英语</p><p>货币代码：CAD</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading has-large-font-size">雇佣合同</h3><p>合同必须使用英语或法语，也可以是双语的。必须书面，并由双方签署。</p><p>&nbsp;&nbsp;合同必须包括：</p><ul><li>员工姓名</li><li>员工地址</li><li>工作角色</li><li>工作描述（包括可衡量的职责）</li><li>开始日期</li><li>报酬</li><li>带薪休假</li><li>福利</li><li>终止</li></ul><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>试用期并非强制性。没有最低试用期。最长试用期为90天（3个月）。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工作时间为每天8小时，每周40小时。标准工作周从星期一到星期五。根据省份和行业的不同，存在特定要求。加班费通常是强制性的。某些职位类型和职业可以豁免加班要求，例如具有管理责任的员工。豁免事项复杂，取决于员工所在的省份。超出标准工作时间的小时被视为加班。员工每周最多可工作8小时的加班。对于额外的工时，员工将获得其工资的150%。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>2022年4月1日，联邦最低工资从每小时15.00美元上调至每小时15.55美元。对于一些省份或地区，如果一般最低工资标准更高，则采用更高标准。</p><p>加拿大的最低工资因省份/地区而异。</p><p>请参考当前工资的相应信息：</p><p>省份</p><ul><li>纽芬兰与拉布拉多</li><li>爱德华王子岛</li><li>新斯科舍</li><li>新不伦瑞克</li><li>魁北克</li><li>安大略</li><li>曼尼托巴</li><li>萨斯喀彻温</li><li>阿尔伯塔</li><li>不列颠哥伦比亚</li></ul><p>地区</p><ul><li>努纳福特</li><li>西北地区</li><li>育空地区</li></ul><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>雇主的成本大致估计在8.23%至14.63%之间，具体取决于员工所在省份。</p><p>以下是安大略省的雇主成本：</p><ul><li>魁北克养老金计划 (仅限魁北克) &#8211; 6.40%</li><li>魁北克父母保险计划 (仅限魁北克) &#8211; 0.69%</li><li>WSDRF &#8211; 劳动力技能发展和认证基金 (仅限魁北克) &#8211; 1.0%</li><li>加拿大养老金计划 (不包括魁北克) &#8211; 5.95%</li><li>就业健康税 &#8211; 根据省政府的规定为1.95%至4.26%</li><li>就业保险 &#8211; 2.28%</li><li>工伤赔偿 &#8211; 根据省政府的规定为0.18%-1.28%</li><li>强制私人医疗保险 &#8211; 124加拿大元至420加拿大元</li><li>医疗保险行政费用 &#8211; 15美元</li></ul><p>可选成本：</p><p>如果向员工提供可选的养老金计划，则将收取以下月费用：</p><ul><li>可选养老金计划的服务费用：10美元</li><li>雇主向员工可选养老金计划的额外服务费 (如果适用)：15美元</li></ul><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading has-large-font-size">终止原因</h3><p>终止必须遵守复杂的规定和员工所在国家的规定。解除员工合同的工作由雇主与主要利益相关者共同处理。这可能包括特定终止情况下的临时费用，以及必要或建议的步骤。</p><p>加拿大的终止程序可能很复杂。在加拿大，雇主不能随意解雇员工，除非在试用期结束后，并且终止必须有正当理由。</p><p>符合规定的终止方式包括：</p><ul><li>员工自愿</li><li>双方协议</li><li>雇主单方面根据以下情况终止：<ul><li>试用期</li><li>对本协议条款的重大违约</li><li>不端行为</li><li>员工破产行为</li><li>员工拒绝按照协议分配的工作职责执行</li></ul></li><li>合同到期</li></ul><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>根据各省份的《就业标准法》，雇员/雇主的法定最低通知期不同。我们的合同考虑到了这一点，并有相应的措辞，旨在保护客户，如果他们希望终止就业协议。</p><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>在加拿大，当员工无故被解雇时，将支付解雇费，当有双方协议终止合作或员工连续工作满12个月时，将支付遣散费。</p><p>在联邦管辖区，每服务一年可获得 2 天工资或 5 天工资中的较高者。遣散费根据特定省份而异。</p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading has-large-font-size">薪资支付</h3><p>每周、每两周或每半月进行一次。 每月定期支付工资是最常见的。</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>工资单包括：</p><ul><li>雇员工资率</li><li>支付期限</li><li>该时期在任何扣除之前和之后的工资（必须详细说明如何计算）</li><li>任何扣除的金额和原因</li></ul><h3 class="wp-block-heading has-large-font-size">年假</h3><p>加拿大各省的休假津贴各不相同。</p><p>兼职员工的带薪休假按照每年标准的 15 个假期日按比例计算。</p><p>这些是最低法定要求，但在加拿大，雇主普遍的做法是提供21天的带薪休假。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>有权利享受病假的时间可能因省份而异。</p><h2 class="wp-block-heading has-large-font-size">育儿假</h2><p>在加拿大，有两种育婴假选择，标准育婴福利或延长育婴福利。加拿大税务局负责支付这些福利。</p><figure class="wp-block-table"><table><tbody><tr><td>福利</td><td>假期</td><td>付款（工资的百分比）</td><td>每周最高限额</td></tr><tr><td>标准育婴福利</td><td>父母双方最多可享受 40 周的福利，但父母一方不能获得超过 35 周的标准福利</td><td>55%</td><td>高达 638 加元</td></tr><tr><td>延长育婴福利</td><td>父母双方最多可享受 69 周的福利，但父母一方不能获得超过 61 周的延长福利</td><td>33%</td><td>高达 383 加元</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading has-large-font-size">个人所得税</h3><p>个人根据所在地缴纳联邦税和省税。联邦个人所得税范围从15%到33%不等。所得税按照渐进税率计算。</p><figure class="wp-block-table"><table><tbody><tr><td>收入（部分）</td><td>税率</td></tr><tr><td>应纳税所得额不超过 53,359 美元</td><td>15%</td></tr><tr><td>应纳税所得额53,359 美元至 106,717 美元</td><td>20.50%</td></tr><tr><td>应纳税所得额 106,717 美元至 165,430 美元</td><td>26%</td></tr><tr><td>应纳税所得额 165,430 美元至 235,675 美元</td><td>29%</td></tr><tr><td>应纳税所得额超过 235,675 美元</td><td>33%</td></tr></tbody></table></figure><p>有关省级和地区级税率，请参阅加拿大国家税务局和魁北克省税务局。</p><h2 class="wp-block-heading has-large-font-size">社会保险</h2><ul><li>养老基金</li><li>社会保障</li><li>残疾保险</li><li>医疗保健 &#8211; ManuLife (强制 &#8211; 青铜计划)</li><li>养老金 &#8211; Wealthsimple (可选)</li><li>医疗保健 &#8211; ManuLife (可选 &#8211; 银和金计划)</li></ul><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><p>在加拿大，公共假期取决于员工所在的省份。有全国性和特定省份的公共假期。全国性公共假期包括：</p><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>日期</td></tr><tr><td>New Year&#8217;s Day    </td><td>1.1</td></tr><tr><td>Canada Day</td><td>7.1</td></tr><tr><td>Labour Day</td><td>9.2</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr></tbody></table></figure>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>