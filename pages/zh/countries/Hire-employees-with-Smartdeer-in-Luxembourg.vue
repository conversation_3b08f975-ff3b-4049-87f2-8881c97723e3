<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在卢森堡雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在卢森堡雇佣',
  ogDescription: '基础信息 首都：卢森堡 luxembourg 时区:GMT+1 语言：法语、德语和卢森堡语 货币代码：EUR […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：卢森堡 luxembourg 时区:GMT+1 语言：法语、德语和卢森堡语 货币代码：EUR […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在卢森堡雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在卢森堡雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/卢森堡-Fi.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/卢森堡国旗.jpg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：卢森堡 luxembourg</p><p>时区:GMT+1</p><p>语言：法语、德语和卢森堡语</p><p>货币代码：EUR</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading">雇佣合同</h3><p>合同可以用英文撰写。合同必须以书面形式，并由双方签署。</p><p>&nbsp;&nbsp;合同必须包括：</p><ul><li>姓名</li><li>开始日期</li><li>就业期限</li><li>职位描述</li><li>终止条件</li><li>月薪</li><li>工作地址</li><li>工作时间</li><li>假期</li><li>奖金</li><li>福利</li></ul><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>最低试用期为90天，最长试用期为180天。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>所有员工都必须支付加班费，除了高级经理。加班费按照正常小时费率的140%支付。加班可能以休假形式支付（1小时加班等于1小时30分钟的休假）。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>最低工资根据员工的技能而异。无技能工人的最低工资通常约为2,570.93欧元，而有技能工人的最低工资约为3,085.12欧元。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>&nbsp;&nbsp;雇主的成本通常估计为员工工资的11.94%。</p><ul><li>公共养老基金 &#8211; 8%</li><li>疾病和产假福利基金（Caisse de maladie soins）- 0.25%</li><li>工伤保险 &#8211; 0.75%</li><li>公共医疗保险（Caisse de maladie soins）- 2.8%</li><li>入职体检和员工专业健康护理 &#8211; 0.14%</li><li>电话和互联网津贴 &#8211; 15欧元</li></ul><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading">终止原因</h3><p>在卢森堡，雇主不可以无故解雇雇员，解雇必须有正当理由。</p><p>&nbsp;&nbsp;符合法规的解雇方式包括：</p><ul><li>雇员自愿离职</li><li>双方协商一致</li><li>雇主根据以下情况单方面解雇：<ul><li>试用期</li><li>客观理由</li><li>纪律解雇</li><li>由于工作不适合而表现不佳</li></ul></li><li>合同到期</li></ul><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>最低通知期为30天，根据雇佣期限和终止类型，通知期将会延长。</p><p>雇主终止通知期：</p><ul><li>如果服务年限少于5年，则为60天</li><li>如果服务年限超过5年但少于10年，则为120天</li><li>如果服务年限超过10年，则为180天</li></ul><p>员工辞职通知期：</p><ul><li>如果服务年限少于5年，则为30天</li><li>如果服务年限超过5年但少于10年，则为60天</li><li>如果服务年限超过10年，则为90天</li></ul><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>在卢森堡，所有在未经适当通知被解雇或已被公司雇用超过5年的员工都有权获得解雇费。解雇费为每年服务30天的工资，最高不超过25个月的基本薪水。</p><ul><li>从第5年到第10年的工作年限，为4个月的工资。</li><li>从第10年到第15年的工作年限，为2个月的工资。</li><li>从第15年到第20年的工作年限，为3个月的工资。</li><li>从第20年到第25年的工作年限，为6个月的工资。</li><li>从第25年到第30年的工作年限，为9个月的工资。</li><li>30年以上的工作年限，为12个月的工资。</li></ul><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading">薪资支付</h3><p>净工资不必支付到卢森堡银行账户或以欧元支付。外国银行账户是可以接受的。工资净额必须在工资发放当月月底之前存入员工的银行账户（例如，1月份的工资应在1月31日之前到达员工手中）。</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>每月月底必须向雇员提供月工资工资单，显示每个应税薪酬项目（例如总薪酬、缴纳的社会保障缴款和预扣税款）。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职员工每年有26个工作日的带薪休假。带薪休假每月累计2.17天。员工在连续工作3个月后有资格享受年假。兼职员工有权获得假期天数的按比例分配。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>员工有权获得无限次数的带薪病假。在前77天将由雇主支付100%的工资。如果第77天是所在月份1号之后，例如3月18日，如果员工继续休病假，雇主需要继续支付该月剩余天数的工资。从病假的第77天后的下个月的第一天起，病假将按照员工的正常工资的100%支付，最高不超过社会保障的最低社会工资x5，由社会保障直接支付。</p><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>员工有权享受20周的带薪产假，分为产前和产后两部分。预产期前必须休8周的产前假。<br>员工在离职前3个月内的最高工资将得到100%，社会保障将负责支付这笔款项。</p><p>员工在产假和/或领养假期结束后有权选择两种育婴假期之一。育婴假最长可达6个月，取决于雇员的就业合同中所列小时数和其就业状态。父母可以决定如何分配休假时间。任何一位父母都可以休育婴假。社会保障将根据员工过去12个月的平均工资计算员工的工资。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading">个人所得税</h3><p>卢森堡的个人所得税复杂。个人所得税税率从8%到42%不等。个人所得税根据渐进税率、婚姻状况和子女数量计算。</p><h3 class="wp-block-heading">社会保险</h3><p>雇主、雇员和个体经营者均需缴纳社会缴款。每个雇主必须向社会保障共同中心（“Centre Commun de la Sécurité Sociale”- CCSS）注册其公司及其每位员工（在入职后八天内）。</p><figure class="wp-block-table"><table><tbody><tr><td>Contribution 贡献</td><td>Employer Contribution (%) 雇主贡献（%）</td><td>&nbsp;&nbsp; Employee Contribution (%) 雇员贡献（%）</td><td>Salary Cap/year (EUR) 工资上限/年（欧元）</td></tr><tr><td>Health Insurance</td><td>3.05</td><td>&nbsp;&nbsp; 3.05</td><td>12,235.34&nbsp;</td></tr><tr><td>Pension Insurance</td><td>8.0</td><td>&nbsp;&nbsp; 8.0</td><td>12,235.34&nbsp;</td></tr><tr><td>Dependency Insurance</td><td>一</td><td>&nbsp;&nbsp; 1.4</td><td>NA</td></tr><tr><td>Health at Work Contribution</td><td>0.14</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 一</td><td>12,235.34&nbsp;</td></tr><tr><td>Accident Insurance</td><td>0.75</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 一</td><td>12,235.34&nbsp;</td></tr><tr><td>Employers Mutual Scheme</td><td>0.53-2.88</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; —</td><td>12,235.34&nbsp;</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>日期</td></tr><tr><td>&nbsp;New Year&#8217;s Day</td><td>1.1</td></tr><tr><td>&nbsp;Easter Monday</td><td>4.1</td></tr><tr><td>&nbsp;Labour Day</td><td>5.1</td></tr><tr><td>&nbsp;Ascension Day</td><td>5.9</td></tr><tr><td>&nbsp;Europe Day</td><td>5.9</td></tr><tr><td>&nbsp;Whit Monday</td><td>5.2</td></tr><tr><td>&nbsp;National Day</td><td>6.23</td></tr><tr><td>&nbsp;Assumption Day</td><td>8.15</td></tr><tr><td>&nbsp;All Saints&#8217;Day</td><td>11.1</td></tr><tr><td>&nbsp;Christmas Day</td><td>12.25</td></tr><tr><td>&nbsp;Second Day of Christmas</td><td>12.26</td></tr></tbody></table></figure><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>