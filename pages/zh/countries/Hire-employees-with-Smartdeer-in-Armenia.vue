<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在亚美尼亚雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在亚美尼亚雇佣',
  ogDescription: '基础信息 首都:埃里温 Yerevan 时区：GMT+4 语言：亚美尼亚语 货币代码：AMD 人力资源制度概况 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都:埃里温 Yerevan 时区：GMT+4 语言：亚美尼亚语 货币代码：AMD 人力资源制度概况 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在亚美尼亚雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在亚美尼亚雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/亚美尼亚FI.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/亚美尼亚flag.jpg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都:埃里温 Yerevan</p><p>时区：GMT+4</p><p>语言：亚美尼亚语</p><p>货币代码：AMD</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading">雇佣合同</h3><p>合同必须用亚美尼亚语书写，可以是双语的。它们必须是书面形式，并由双方签署。合同必须包括：</p><ul><li>姓名</li><li>开始日期</li><li>雇佣期限</li><li>职位描述</li><li>终止条件</li></ul><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>试用期不是强制性的。最长试用期为3个月。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工时为每天8小时，每周40小时。标准工作周从星期一到星期五。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>最低工资为每月68,000亚美尼亚德拉姆。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>$0</p><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h2 class="wp-block-heading has-large-font-size">终止原因</h2><p><em>解雇必须遵守复杂的规定和员工所在国家的规定。解雇始终由雇主与主要利益相关者处理。它可能包括特定解雇案件的特定费用以及所需或建议的步骤。</em></p><p>亚美尼亚的解雇可能会变得复杂。在亚美尼亚，雇主不能随意解雇员工，解雇必须有正当原因。</p><p>符合规定的解雇包括：</p><ul><li>员工自愿离职</li><li>双方同意</li><li>雇主单方面解雇，基于：<ul><li>试用期</li><li>客观原因</li><li>纪律性解雇</li><li>工作不适任</li></ul></li><li>合同到期</li></ul><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>最短通告期为14天，将根据雇佣的时间长度增加。</p><ul><li>1年的服务时间为2周</li><li>1-5年的服务时间为35天</li><li>5-10年的服务时间为42天</li><li>10-15年的服务时间为49天</li><li>15年以上的服务时间为60天</li></ul><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>在亚美尼亚，离职费取决于解雇类型和雇佣时间的长短。</p><p>由于以下原因被解雇的所有员工有权获得1个月的离职费：</p><ul><li>组织清算</li><li>员工数量和员工削减</li><li>生产量和经济和工作组织条件的变化</li></ul><p>因与强制兵役、长期残疾、老年或工作条件重大变化有关的原因而被解雇的员工将根据雇佣时间长短获得10 &#8211; 44个工作日的不等离职费。</p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading">薪资支付</h3><p>每月一次</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职和兼职员工均有权每年获得20个工作日的带薪休假（PTO）。PTO每月累积1.67天。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>员工有权获得最长120天的带薪病假。这种休假按长度由不同支付者以不同的费率支付：</p><figure class="wp-block-table"><table><tbody><tr><td>时间</td><td>费用</td><td>支付方</td></tr><tr><td>1天</td><td>0%</td><td>N/A</td></tr><tr><td>2 &#8211; 5天</td><td>100%</td><td>雇主</td></tr><tr><td>6 &#8211; 120天</td><td>基本工资的80%</td><td>雇主</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>怀孕的员工有权获得140天的带薪产假。必须在孩子出生前休假70天，其余的天数必须在孩子出生后休假。在此期间，员工将获得100%的平均工资，雇主将负责支付此薪水。</p><p>在亚美尼亚，没有法律涵盖育婴假。然而，员工可能有资格获得产假和陪产假。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading">个人所得税</h3><p>个人所得税适用于2022年的单一固定税率为21%。从2023年1月1日起，将调整为20%。</p><h3 class="wp-block-heading">社会保险</h3><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>