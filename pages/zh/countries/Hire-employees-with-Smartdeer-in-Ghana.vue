<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在加纳雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在加纳雇佣',
  ogDescription: '基础信息 首都：阿克拉 Accra 时区：GMT 语言：英语 货币代码：GHS 人力资源制度概况 雇佣合同 合 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：阿克拉 Accra 时区：GMT 语言：英语 货币代码：GHS 人力资源制度概况 雇佣合同 合 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在加纳雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在加纳雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/06/加纳-FI.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/06/加纳国旗.jpg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：阿克拉 Accra</p><p>时区：GMT</p><p>语言：英语</p><p>货币代码：GHS</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading has-large-font-size">雇佣合同</h3><p>合同必须是英文的。必须是书面形式，并由双方签字。<br>合同必须包括:<br>——名字<br>-开始日期<br>-受雇期限<br>-职位描述<br>-终止条件</p><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>标准的试用期在90天到180天之间。但是，《劳动法》中并没有明确规定试用期的最长期限，这将在雇佣协议中确定。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工作时间为每天8小时，每周40小时。标准的工作周是从星期一到星期五。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>最低工资为每月365.31英镑。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>雇主成本一般估计为雇员工资的9.16%。<br>-社会保障和养老金- 9.16%</p><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading has-large-font-size">终止原因</h3><p><br>合规终止包括:<br>-由员工自愿<br>-经双方同意<br>-由雇主单方面根据:<br>-缺乏工作资格<br>-不当行为<br>-冗余<br>-禁止工人从事该工作的法律限制。</p><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>最低通知期为14天，并将根据雇佣时间的长短而延长。<br>-如服务年资少于3年，则为14天<br>-如服务年资超过3年，则为30天</p><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>在加纳，对遣散费没有法定要求。在通知期内，员工将获得标准工资。在裁员的情况下，需要由雇员和雇主协商。</p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h2 class="wp-block-heading has-large-font-size">薪资支付</h2><p>根据《劳动法》，雇主有义务按时以法定货币支付员工工资。按月工作的员工必须按月支付工资，按周工作的员工必须按周支付工资。该法还允许以实物形式支付工资。</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>加纳没有法律规定雇主必须向雇员提供工资单。但是，建议采取这种做法。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职和兼职员工每年都有15个工作日的带薪休假。PTO按每月1.25天计算。工作满12个月的员工有资格享受年假。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>《劳动法》对病假没有明确规定，通常取决于公司的政策。然而，出示医疗证明的员工可以不带薪休假。</p><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>员工有12周的带薪休假。在此期间，员工将获得100%的工资，由雇主负责支付。<br>如果员工患有与怀孕有关的疾病或怀孕超过一个孩子，员工可以额外延长休假2周，并获得100%的工资。</p><p>在加纳，没有法律涵盖育儿假。但是，员工可以享受产假和陪产假。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading has-large-font-size">个人所得税</h3><p>个人所得税从0%到35%不等。所得税是按累进税率计算的。许多其他因素可能影响总体比率，例如家庭状况和子女数量。</p><h3 class="wp-block-heading has-large-font-size">社会保险</h3><p>社会保障和国民保险信托 (SSNIT) 负责管理加纳的社会保障和国民保险制度。雇员和雇主都必须向 SSNIT 缴纳费用。雇员缴纳基本工资的 5.5%，雇主缴纳 13%。此外，加纳还有一项自愿公积金计划。这允许雇主和雇员缴纳高达雇员基本工资的 16.5%。</p><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>日期</td></tr><tr><td>New Year&#8217;s Day</td><td>1.1</td></tr><tr><td>Constitution Day</td><td>1.8</td></tr><tr><td>ndependence Day</td><td>3.6</td></tr><tr><td>Good Friday</td><td>3.29</td></tr><tr><td>Easter Monday</td><td>4.1</td></tr><tr><td>Eid al-Fitr*</td><td>4.1</td></tr><tr><td>May Day</td><td>5.1</td></tr><tr><td>Eid gl-Adhg*</td><td>6.17</td></tr><tr><td>Founders&#8217;Day</td><td>8.5</td></tr><tr><td>Kwame Nkrumah Memorial Day</td><td>9.23</td></tr><tr><td>Farmers&#8217;Day</td><td>12.6</td></tr><tr><td>Christmas Day</td><td>12.25</td></tr><tr><td>Boxing Day</td><td>12.26</td></tr></tbody></table></figure><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>