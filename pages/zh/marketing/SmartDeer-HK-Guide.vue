<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
    .countries-content
      h1.article-title 收藏！中企出海指南（香港篇）新鲜出炉！
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '收藏！中企出海指南（香港篇）新鲜出炉！',
  ogDescription: '      重磅推出！全球智库力作——SmartDeer《中企出海国家/地区指南·香港篇》震撼上线，深度解码香 […]',
  ogSiteName: 'SmartDeer',
  description: '      重磅推出！全球智库力作——SmartDeer《中企出海国家/地区指南·香港篇》震撼上线，深度解码香 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '收藏！中企出海指南（香港篇）新鲜出炉！'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '收藏！中企出海指南（香港篇）新鲜出炉！';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2025/05/香港1.png';
const flagImage = '';
const htmlContent = '<p><div><section data-pm-slice="0 0 []" style="-webkit-tap-highlight-color: transparent; margin: 0px 0px 24px; padding: 0px; outline: 0px; max-width: 100%; color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 17px; letter-spacing: 0.544px; text-align: justify; white-space-collapse: collapse; background-color: rgb(255, 255, 255); visibility: visible; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span leaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; visibility: visible;">      <span textstyle="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; visibility: visible;">重磅推出！全球智库力作——SmartDee</span><span textstyle="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-size: 16px; visibility: visible; box-sizing: border-box !important; overflow-wrap: break-word !important;">r</span><span textstyle="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-weight: bold; visibility: visible;">《中企出海国家/地区指南·香港篇》</span><span textstyle="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; visibility: visible;">震撼上线，深度解码香港市场机遇、政策红利与合规实战要点。      后续还将持续推出</span><span textstyle="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-weight: bold; visibility: visible;">更多国家与地区攻略</span><span textstyle="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; visibility: visible;">，用最实用的出海干货助您抢占全球商机。联系我们，一键获取全系列最新指南，开启高效出海之旅！</span></span></section></div></p><figure class="wp-block-image size-full"><img loading="lazy" decoding="async" width="580" height="597" src="https://blog.smartdeer.work/wp-content/uploads/2025/05/香港篇.png" alt="" class="wp-image-1805"/></figure>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>