/*
 * @Author: sx <EMAIL>
 * @Date: 2022-12-14 16:02:20
 * @LastEditors: sx <EMAIL>
 * @LastEditTime: 2023-01-13 13:02:16
 * @FilePath: \bpo-website-mobile\assets\utils\lang.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const LANG_COOKIE_KEY = 'sd_intl_lang'

type Lang = 'en' | 'zh'

// 首页引导
export function indexGuide() {
  const config = useRuntimeConfig().public
  const result = window.matchMedia("(max-width: 700px)")
  const domain = result.matches ? config.mobile_site_host : config.pc_site_host

  // 这里只是获取语言的部分，国家的部分省略，不排除未来有其他的逻辑。
  const systemLang = window.navigator.language.substring(0, 2)

  // 这里cookie采用的是nuxt自带的，后续可以更换为其他库
  const userLang = useCookie(LANG_COOKIE_KEY)
  const lang = userLang.value ? userLang.value : systemLang

  const page = `${domain ? domain : ''}/${lang}`
  location.replace(page)
}

// 切换语言，同时埋入页面的的路径，和屏幕判断。
// 如果没有传入path，则表示，该页面没有多语言的对应页面。
export function swithLang(lang: Lang, path: string = '') {
  const userLang = useCookie(LANG_COOKIE_KEY)
  userLang.value = lang

  const config = useRuntimeConfig().public
  const result = window.matchMedia("(max-width: 700px)")
  const domain = result.matches ? config.mobile_site_host : config.pc_site_host

  const page = `${domain ? domain : ''}/${lang}${path ? path : ''}`
  location.replace(page)
}

export default {
  indexGuide,
  swithLang
}