header {
  background: #fff6ec;
  .header-banner {
    position: relative;
    height: 300px;
    margin: 0 15px;
  }

  .header-banner-text {
    position: absolute;
    left: 0;
    z-index: 2;
    .header-title {
      font-size: 24px;
      font-weight: bold;
      line-height: 32px;
      margin-top: 20px;
    }
    .header-desc {
      margin-top: 20px;
      font-size: 12px;
      line-height: 18px;
      font-weight: 300;
      width: 220px;
    }
    .header-contact {
      margin-top: 20px;
      width: 80px;
      height: 36px;
      border-radius: 28px;
      background: #474747;
      font-size: 12px;
      text-align: center;
      color: #FFF;
      cursor: pointer;
    }
  }

  .header-banner-image {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 340px;
    z-index: 1;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.country-index-main {
  margin: 40px 15px 30px;

  h2 {
    font-size: 18px;
    line-height: 26px;
    letter-spacing: 1px;
    text-align: center;
  }

  p {
    font-size: 10px;
    font-weight: 350;
    line-height: 15px;
    letter-spacing: 1px;
  }
  .search-bar {
    margin: 20px auto 0;
    width: 335px;
    height: 44px;
    border: 1px solid #EFF0F6;
    box-sizing: border-box;
    border-radius: 40px;
    box-shadow: 0 8px 25px 0 rgba(13, 10, 44, 0.06);

    .text-search {
      border: none;
      outline: none;
      font-size: 14px;
      background: #FFF;
      width: 260px;
      line-height: 28px;
      margin-top: 6px;
      margin-left: 20px;
    }

    .btn-search {
      cursor: pointer;
      background-color: #FE9111;
      width: 36px;
      height: 36px;
      font-size: 0;
      border: none;
      border-radius: 56px;
      background-repeat: no-repeat;
      background-position: center;
      vertical-align: middle;
      background-image: url("~/assets/images/countries/search.png");
      background-size: 50%;
    }
  }

  .country-list {
    margin-top: 40px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 20px;
    a {
      display: block;
      width: calc(50% - 10px);
      height: 148px;
      box-sizing: border-box;
      border: 1px solid #EFF0F6;
      box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.1);
      border-radius: 10px;
      overflow: hidden;
    }

    li{
      list-style: none;
      img {
        width: 100%;
        height: 120px;
      }

      .link-container {
        font-size: 10px;
        text-align: center;
        line-height: 26px;
        padding: 0 10px;
        height: 26px;
        white-space: normal;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #000;
      }
    }
  }

  .paginator {
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    margin: 20px 0;
  }
}