<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Open Graph Meta Tags 测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .input-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="url"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #005a87;
        }
        .results {
            margin-top: 20px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border-left: 4px solid #007cba;
        }
        .meta-tag {
            margin-bottom: 10px;
            padding: 8px;
            background-color: white;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        }
        .meta-tag strong {
            color: #007cba;
        }
        .error {
            color: #d32f2f;
            background-color: #ffebee;
            border-left-color: #d32f2f;
        }
        .success {
            color: #388e3c;
            background-color: #e8f5e8;
            border-left-color: #388e3c;
        }
        .preview {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: white;
        }
        .preview img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
        }
        .loading {
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Open Graph Meta Tags 测试工具</h1>
        <p>输入页面URL来检查Open Graph meta标签配置，特别是Telegram链接预览所需的标签。</p>
        
        <div class="input-group">
            <label for="url">页面URL:</label>
            <input type="url" id="url" placeholder="http://localhost:3000/zh" value="http://localhost:3000/zh">
        </div>
        
        <button onclick="checkMetaTags()">检查 Meta 标签</button>
        
        <div id="results"></div>
    </div>

    <script>
        async function checkMetaTags() {
            const url = document.getElementById('url').value;
            const resultsDiv = document.getElementById('results');
            
            if (!url) {
                resultsDiv.innerHTML = '<div class="results error">请输入有效的URL</div>';
                return;
            }
            
            resultsDiv.innerHTML = '<div class="results loading">正在检查...</div>';
            
            try {
                // 由于CORS限制，我们需要使用代理或者直接在同域下测试
                const response = await fetch(url);
                const html = await response.text();
                
                // 解析HTML
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                
                // 提取所有相关的meta标签
                const metaTags = [];
                const ogTags = doc.querySelectorAll('meta[property^="og:"]');
                const twitterTags = doc.querySelectorAll('meta[name^="twitter:"]');
                const descriptionTag = doc.querySelector('meta[name="description"]');
                
                // 收集Open Graph标签
                ogTags.forEach(tag => {
                    metaTags.push({
                        type: 'Open Graph',
                        property: tag.getAttribute('property'),
                        content: tag.getAttribute('content')
                    });
                });
                
                // 收集Twitter标签
                twitterTags.forEach(tag => {
                    metaTags.push({
                        type: 'Twitter',
                        property: tag.getAttribute('name'),
                        content: tag.getAttribute('content')
                    });
                });
                
                // 收集描述标签
                if (descriptionTag) {
                    metaTags.push({
                        type: 'Basic',
                        property: 'description',
                        content: descriptionTag.getAttribute('content')
                    });
                }
                
                displayResults(metaTags, url);
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="results error">错误: ${error.message}</div>`;
            }
        }
        
        function displayResults(metaTags, url) {
            const resultsDiv = document.getElementById('results');
            
            if (metaTags.length === 0) {
                resultsDiv.innerHTML = '<div class="results error">未找到相关的meta标签</div>';
                return;
            }
            
            let html = '<div class="results success"><h3>找到的 Meta 标签:</h3>';
            
            metaTags.forEach(tag => {
                html += `
                    <div class="meta-tag">
                        <strong>[${tag.type}]</strong> ${tag.property}: <br>
                        <span style="color: #666;">${tag.content}</span>
                    </div>
                `;
            });
            
            // 检查关键标签
            const ogImage = metaTags.find(tag => tag.property === 'og:image');
            const ogTitle = metaTags.find(tag => tag.property === 'og:title');
            const ogDescription = metaTags.find(tag => tag.property === 'og:description');
            const ogUrl = metaTags.find(tag => tag.property === 'og:url');
            
            html += '<h3>Telegram 链接预览检查:</h3>';
            
            if (ogImage && ogImage.content.startsWith('http')) {
                html += '<div class="meta-tag success">✅ og:image 使用绝对URL</div>';
                html += `<div class="preview"><img src="${ogImage.content}" alt="预览图片" onerror="this.style.display=\'none\'"></div>`;
            } else if (ogImage) {
                html += '<div class="meta-tag error">❌ og:image 使用相对路径，Telegram可能无法显示</div>';
            } else {
                html += '<div class="meta-tag error">❌ 缺少 og:image 标签</div>';
            }
            
            if (ogTitle) {
                html += '<div class="meta-tag success">✅ 包含 og:title</div>';
            } else {
                html += '<div class="meta-tag error">❌ 缺少 og:title</div>';
            }
            
            if (ogDescription) {
                html += '<div class="meta-tag success">✅ 包含 og:description</div>';
            } else {
                html += '<div class="meta-tag error">❌ 缺少 og:description</div>';
            }
            
            if (ogUrl) {
                html += '<div class="meta-tag success">✅ 包含 og:url</div>';
            } else {
                html += '<div class="meta-tag error">❌ 缺少 og:url</div>';
            }
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }
        
        // 页面加载时自动检查
        window.onload = function() {
            checkMetaTags();
        };
    </script>
</body>
</html>
