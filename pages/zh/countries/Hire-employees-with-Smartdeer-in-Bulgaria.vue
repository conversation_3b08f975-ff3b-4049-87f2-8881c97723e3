<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在保加利亚雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在保加利亚雇佣',
  ogDescription: '基础信息 首都：索非亚 Sofia 时区：GMT+2 语言：保加利亚语 货币代码：BGN 人力资源制度概况 雇 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息 首都：索非亚 Sofia 时区：GMT+2 语言：保加利亚语 货币代码：BGN 人力资源制度概况 雇 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在保加利亚雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在保加利亚雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/保加利亚-FI.jpg';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/保加利亚国旗.png';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>首都：索非亚 Sofia</p><p>时区：GMT+2</p><p>语言：保加利亚语</p><p>货币代码：BGN</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading">雇佣合同</h3><p>&nbsp;&nbsp;合同必须用保加利亚语书写，可以是双语。必须书面签署双方。合同必须包括：</p><ul><li>员工的姓名和个人识别号</li><li>基本月薪</li><li>签署日期</li><li>开始日期</li><li>试用期</li><li>通知期</li><li>工作职位：名称和国家职位代码</li><li>工作时间</li><li>工作场所</li></ul><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>试用期不是强制性的。最短试用期为30天。最长试用期为180天（6个月）。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工作时间为每天8小时，每周40小时。标准工作周为周一至周五。</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><p>保加利亚的最低工资为933 BGN。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>雇主成本通常估计在员工工资的19％之间。</p><ul><li>医疗保险 &#8211; 4.80％*</li><li>职业事故和职业病基金 &#8211; 0.50％*</li><li>普通疾病和产假基金 &#8211; 2.10％*</li><li>失业基金 &#8211; 0.60％*</li><li>养老金基金（NSSI） &#8211; 8.22％*</li><li>通用养老金基金的补充强制养老保险 &#8211; 2.80％*</li><li>医疗检查评估和健康与安全 &#8211; 5保加利亚列弗</li></ul><p>&nbsp;&nbsp;*计算缴纳金额的最高工资为3750 BGN。</p><ul><li>一次性费用<ul><li>EA交付的快递费 &#8211; 20保加利亚列弗</li><li>强制性湿墨雇佣协议处理费 &#8211; 80美元</li></ul></li><li>年费<ul><li>风险评估 &#8211; 35保加利亚列弗</li><li>劳动安全公司保险 &#8211; 25保加利亚列弗</li></ul></li></ul><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading">终止原因</h3><p>在保加利亚，解雇可能很复杂。没有随意解雇的情况，而且不能没有原因地终止合同。</p><p>符合条件的终止包括：</p><ul><li>员工自愿离职</li><li>双方协议</li><li>雇主基于以下原因单方面解雇：<ul><li>试用期</li><li>纪律解雇</li><li>企业关闭</li><li>减少工作量。</li></ul></li><li>合同到期</li></ul><p>未休的假期必须在解雇时支付，以及任何未支付的佣金或奖金。</p><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>双方的最短通知期为30天，最长可延长至90天。</p><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>&nbsp;&nbsp;在保加利亚，员工有权获得离职补偿金。离职补偿金根据终止协议的原因而异：</p><ul><li>雇主关闭实体或减少人员：必须支付一个月的离职补偿金。</li><li>雇主因健康问题或对员工的健康构成风险而终止雇佣关系，如果员工工作满五年：必须支付两个月的离职补偿金。</li><li>员工达到退休年龄：<ul><li>工作年限少于10年：三个月的离职补偿金</li><li>工作年限为10年或更长时间：六个月的离职补偿金</li></ul></li><li>合同终止，雇主以支付补偿为条件达成双方协议：必须支付至少四个月的离职补偿金。</li><li>雇主在不提供适当通知的情况下终止合同：必须支付三个月的离职补偿金。</li></ul><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading">薪资支付</h3><p>保加利亚的工资发放周期一般为月度，工资在每月的最后一个工作日发放。</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>在保加利亚，向员工提供数字工资单是合法的。但是，员工必须确认收到工资单。通常，工资单以纸质形式提供，员工在收到工资单后签字。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>全职和兼职员工每年有权获得20天带薪休假（PTO）。带薪休假每月累计1.67天。</p><p>如果兼职员工每天工作少于四小时，带薪休假将按工作时间比例计算。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>在保加利亚，病假的支付方式和支付方式如下：</p><ul><li>雇主支付头3天的病假，支付工资的约70％。</li><li>从第4天起连续的病假，由政府支付（NSSI国家社会保障基金）。这些天的工资为过去18个月的平均日收入的80％。</li></ul><p>员工有权获得最多180天的带薪病假。如果员工经特别医疗委员会（ТЕЛК）评估后，病假可以延长超过180天。</p><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>怀孕员工，在连续工作12个月后，有权获得410天的带薪休假。员工必须在孩子出生前休息45天，出生后休息45天。其余天数可由员工自行决定，直到孩子两岁。</p><p>员工在此期间的补偿取决于休假时间。国家社会保险机构负责支付，并直接支付给员工。</p><figure class="wp-block-table"><table><tbody><tr><td>时期（儿童年龄）</td><td>支付</td><td>付款人</td></tr><tr><td>0 &#8211; 1 年</td><td>90% 薪资</td><td>国家保险协会</td></tr><tr><td>1 &#8211; 2 岁</td><td>BGN 380,00 每月</td><td>国家保险协会</td></tr></tbody></table></figure><p>在保加利亚，没有法律规定育婴假。但是，员工可能有权利产假和陪产假。员工可以通过以下方式延长休假：</p><ul><li>在休假6个月后，将产假，包括现金福利，转给伴侣。</li><li>请求国家社会保险机构支付的育儿津贴。</li></ul><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading">个人所得税</h3><p>个人所得税是10％的统一税率。</p><p>注意：除此之外，员工还必须向社会保障缴纳13.78％的费用。</p><h3 class="wp-block-heading">社会保险</h3><p>保加利亚的社会保险由国家社会保障局（NSSI）管理。社会保险制度涵盖养老金、疾病津贴、残疾津贴、产假、丧葬费、失业保险和儿童保育福利。</p><p>雇主还必须缴纳公共医疗保险，该保险由国家健康保险基金 (NHIF) 管理。社会保障缴款和医疗保险缴款由国家税务局 (NRA) 征收。</p><figure class="wp-block-table"><table><tbody><tr><td>社会保险</td><td>雇主支付（%）</td><td> 雇员支付（%）</td></tr><tr><td>Health insurance</td><td>&nbsp; 4.80</td><td>&nbsp;3.20</td></tr><tr><td>Pension</td><td>&nbsp; 8.22</td><td>&nbsp;6.58</td></tr><tr><td>Unemployment insurance</td><td>&nbsp; 0.60</td><td>&nbsp;0.40</td></tr><tr><td>lllness and maternity</td><td>&nbsp; 2.10</td><td>&nbsp;1.40</td></tr><tr><td>Supplementary pension</td><td>&nbsp; 2.80</td><td>&nbsp;2.20</td></tr><tr><td>Occupational/accident insurance</td><td>&nbsp; 0.40-1.10</td><td>&nbsp;一</td></tr><tr><td>TOTAL</td><td>&nbsp; 18.92-19.62</td><td>13.78</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">2024年法定假期</h2><figure class="wp-block-table"><table><tbody><tr><td>假期</td><td>&nbsp; 日期</td></tr><tr><td>New Year&#8217;s Day</td><td>1.1</td></tr><tr><td>Liberation Day</td><td>3.3</td></tr><tr><td>Liberation Day Holiday</td><td>3.4</td></tr><tr><td>Labor Day</td><td>&nbsp; 5.1</td></tr><tr><td>Orthodox Good Friday</td><td>&nbsp; 5.3</td></tr><tr><td>Orthodox Easter Saturday</td><td>&nbsp; 5.4</td></tr><tr><td>Orthodox Easter Sunday</td><td>&nbsp; 5.5</td></tr><tr><td>Orthodox Easter Monday</td><td>&nbsp; 5.6</td></tr><tr><td>St. George’s Day</td><td>&nbsp; 5.6</td></tr><tr><td>Culture and Literacy Day</td><td>5.24</td></tr><tr><td>Unification Day</td><td>9.6</td></tr><tr><td>Independence Day</td><td>9.22</td></tr><tr><td>Independence Day Holiday</td><td>9.23</td></tr><tr><td>Day of the Bulgarian Enlighteners</td><td>11.1</td></tr><tr><td>Christmas Eve</td><td>12.24&nbsp;</td></tr><tr><td>Christmas Day</td><td>12.25&nbsp;</td></tr><tr><td>Second Day of Christmas</td><td>12.26&nbsp;</td></tr></tbody></table></figure><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>