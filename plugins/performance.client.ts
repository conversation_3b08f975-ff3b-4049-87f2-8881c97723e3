// 性能优化插件 - 客户端
export default defineNuxtPlugin(() => {
  // 图片懒加载优化
  const setupImageLazyLoading = () => {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            
            // 加载图片
            if (img.dataset.src) {
              img.src = img.dataset.src
              img.removeAttribute('data-src')
            }
            
            // 加载 srcset
            if (img.dataset.srcset) {
              img.srcset = img.dataset.srcset
              img.removeAttribute('data-srcset')
            }
            
            // 添加加载完成类
            img.addEventListener('load', () => {
              img.classList.add('loaded')
            })
            
            imageObserver.unobserve(img)
          }
        })
      }, {
        rootMargin: '50px 0px',
        threshold: 0.01
      })
      
      // 观察所有懒加载图片
      const lazyImages = document.querySelectorAll('img[data-src]')
      lazyImages.forEach(img => imageObserver.observe(img))
    }
  }
  
  // 预加载关键资源
  const preloadCriticalResources = () => {
    const criticalResources = [
      '/images/logo.png',
      '/images/tg_banner.png'
    ]
    
    criticalResources.forEach(resource => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'image'
      link.href = resource
      document.head.appendChild(link)
    })
  }
  
  // 优化字体加载
  const optimizeFontLoading = () => {
    // 预加载关键字体
    const fontPreloads = [
      'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
    ]
    
    fontPreloads.forEach(fontUrl => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'style'
      link.href = fontUrl
      link.onload = () => {
        link.rel = 'stylesheet'
      }
      document.head.appendChild(link)
    })
  }
  
  // 优化第三方脚本加载
  const optimizeThirdPartyScripts = () => {
    // 延迟加载非关键脚本
    const delayedScripts = [
      {
        src: 'https://hm.baidu.com/hm.js?your_baidu_analytics_id',
        delay: 3000
      }
    ]
    
    delayedScripts.forEach(script => {
      setTimeout(() => {
        const scriptElement = document.createElement('script')
        scriptElement.src = script.src
        scriptElement.async = true
        document.head.appendChild(scriptElement)
      }, script.delay)
    })
  }
  
  // 监控 Core Web Vitals
  const monitorCoreWebVitals = () => {
    if ('PerformanceObserver' in window) {
      // 监控 LCP (Largest Contentful Paint)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        
        // 发送 LCP 数据到分析服务
        console.log('LCP:', lastEntry.startTime)
        
        // 可以发送到 Google Analytics 或其他分析服务
        if (window.gtag) {
          window.gtag('event', 'web_vitals', {
            name: 'LCP',
            value: Math.round(lastEntry.startTime),
            event_category: 'Web Vitals'
          })
        }
      })
      
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      
      // 监控 FID (First Input Delay)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach(entry => {
          console.log('FID:', entry.processingStart - entry.startTime)
          
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              name: 'FID',
              value: Math.round(entry.processingStart - entry.startTime),
              event_category: 'Web Vitals'
            })
          }
        })
      })
      
      fidObserver.observe({ entryTypes: ['first-input'] })
      
      // 监控 CLS (Cumulative Layout Shift)
      let clsValue = 0
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach(entry => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        })
        
        console.log('CLS:', clsValue)
        
        if (window.gtag) {
          window.gtag('event', 'web_vitals', {
            name: 'CLS',
            value: Math.round(clsValue * 1000),
            event_category: 'Web Vitals'
          })
        }
      })
      
      clsObserver.observe({ entryTypes: ['layout-shift'] })
    }
  }
  
  // 优化滚动性能
  const optimizeScrollPerformance = () => {
    let ticking = false
    
    const updateScrollElements = () => {
      // 在这里处理滚动相关的 DOM 更新
      ticking = false
    }
    
    const onScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollElements)
        ticking = true
      }
    }
    
    window.addEventListener('scroll', onScroll, { passive: true })
  }
  
  // 页面可见性 API 优化
  const handleVisibilityChange = () => {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // 页面隐藏时暂停非关键操作
        console.log('Page hidden - pausing non-critical operations')
      } else {
        // 页面可见时恢复操作
        console.log('Page visible - resuming operations')
      }
    })
  }
  
  // 在页面加载完成后执行优化
  if (process.client) {
    // 立即执行的优化
    preloadCriticalResources()
    optimizeFontLoading()
    
    // DOM 加载完成后执行
    document.addEventListener('DOMContentLoaded', () => {
      setupImageLazyLoading()
      optimizeScrollPerformance()
      handleVisibilityChange()
    })
    
    // 页面完全加载后执行
    window.addEventListener('load', () => {
      optimizeThirdPartyScripts()
      monitorCoreWebVitals()
    })
  }
})

// 类型声明
declare global {
  interface Window {
    gtag?: (...args: any[]) => void
  }
}
