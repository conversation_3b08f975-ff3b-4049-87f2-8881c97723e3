# SmartDeer Website Robots.txt
# 针对百度和必应优化的 robots.txt 文件

User-agent: *
Allow: /

# 允许所有搜索引擎访问主要内容
Allow: /zh/
Allow: /en/
Allow: /ja/
Allow: /images/
Allow: /static/

# 禁止访问敏感或不必要的目录
Disallow: /admin/
Disallow: /api/
Disallow: /_nuxt/
Disallow: /node_modules/
Disallow: /.git/
Disallow: /test/
Disallow: /private/

# 百度爬虫特定设置
User-agent: Baiduspider
Allow: /
Allow: /zh/
Allow: /images/
Crawl-delay: 1

# 必应爬虫特定设置
User-agent: bingbot
Allow: /
Allow: /en/
Allow: /images/
Crawl-delay: 1

# Google 爬虫设置
User-agent: Googlebot
Allow: /
Crawl-delay: 1

# 360 搜索爬虫设置
User-agent: 360Spider
Allow: /
Allow: /zh/
Crawl-delay: 2

# 搜狗爬虫设置
User-agent: Sogou web spider
Allow: /
Allow: /zh/
Crawl-delay: 2

# Sitemap 位置 - Mobile版本
Sitemap: https://m.smartdeer.work/sitemap.xml
Sitemap: https://m.smartdeer.work/sitemap-zh.xml
Sitemap: https://m.smartdeer.work/sitemap-en.xml
Sitemap: https://m.smartdeer.work/sitemap-ja.xml

# 主机验证
Host: m.smartdeer.work
