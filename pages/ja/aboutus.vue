<template lang="pug">
mixin header-nav
  section.header-nav
    NuxtLink(to="/")
      figure.logo
        img(src="~/assets/images/aboutus/sd_logo.png")
    .extra
      .contact-us(@click="()=>{status.showForm = true}")
        .text お問い合わせ

      //- 吃瓜就要付出代价，否则影响页面跳转
      //- https://github.com/element-plus/element-plus/pull/9731
      client-only
        el-dropdown.language-selector
          .text(style="color: #000;") 中 / 英 / 日
          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(@click="switchLang('zh')") 中文 / Chinese
              el-dropdown-item(@click="switchLang('en')") English
              el-dropdown-item(@click="switchLang('ja')") 日本語

mixin company-profile 
  section.company-profile
    .section-title
      h2.title グローバルHRソリューションのパートナー

    .profile-content
      figure
        img(src="~/assets/images/aboutus/company-profile.webp")
      h4 会社概要
      p SmartDeerは、グローバル採用・雇用ソリューションを一体的に提供する「HRサービス＆SaaS」プラットフォームです。 SmartDeerはZixin Capitalのインキュベーションにより、WeLight Fund、WeWork、Hash Globalなどから出資を受けています。 地域の壁を越えて世界中の人材を迅速に採用し、入社から退職、給与、税務、福利厚生まで、雇用ライフサイクル全体を包括的に管理します。 プラットフォームは、各プロセスにおいてコンプライアンスと効率性を担保し、グローバルチームの管理をシンプルにします。
      p また、SmartDeerは国際的に権威ある情報セキュリティマネジメントシステム「ISO 27001」認証を取得済みであり、お客様のデータ安全・プライバシーを徹底保護し、信頼性の高いサービスを提供しています。
      h4 グローバル展開
      p 香港とシンガポールに拠点を持つ当社は、グローバル戦略上の強みを享受しつつ、各地域のデータ保管・処理規制にも完全対応しています。米国、英国、UAE、サウジアラビア、オーストラリア、日本、韓国、タイ、マレーシア、インドネシア、フィリピン、ベトナム、中国本土、メキシコ、ブラジルなどに現地法人・支店を設置しているほか、自社ネットワークとパートナーネットワークを通じて、150以上の国と地域でサービスを展開しています。
      h4 私たちのチーム
      p SmartDeerは、世界10カ国以上に150名以上の従業員を擁し、多言語でのサポートを提供しています。特に中国語と英語のバイリンガル対応に加え、現地語にも強みを持ち、各地域のニーズに柔軟に対応可能です。各国の法規制にも深く精通しており、地域に根ざした専門的なサービスとグローバル対応の両立を実現しています。
      h4 サービス範囲
      p 当社は、採用支援、グローバル雇用コンプライアンス、ビザ取得、給与管理、福利厚生および税務対応を含む包括的なHRソリューションを提供しています。SmartDeerの強力なグローバルHR SaaSシステムにより、企業は複雑なグローバル人事業務を効率化し、プロセスを簡素化、コンプライアンスリスクを軽減できます。プラットフォームを活用することで、企業は世界中での人材採用・入退社・有給休暇・給与・税務を一括管理し、グローバルなビジネス成功を支援します。

mixin profession
  section.profession
    .profession-list
      .profession-item
        figure 
          img(src="~/assets/images/aboutus/earth.svg")
        .content
          .title グローバルな連携オフィス体制
          .desc アメリカ、イギリス、UAE、サウジアラビア、オーストラリア、シンガポール、日本、韓国、タイ、マレーシア、インドネシア、フィリピン、ベトナム、中国本土、メキシコ、香港などに現地のオペレーションチームを展開しています。

      .profession-item
        figure 
          img(src="~/assets/images/aboutus/team.svg")
        .content
          .title 10年以上の実務経験を持つ専門チーム
          .desc 世界各国出身の人材が在籍し、10年以上の本社人事部での勤務経験やHRコンサルティング実績を有する専門家が多数在籍しています。

      .profession-item
        figure 
          img(src="~/assets/images/aboutus/cover.svg")
        .content
          .title サービス範囲の完全網羅
          .desc グローバルな人材採用、正社員雇用、フレキシブルな契約雇用、人事業務のBPO（業務委託）まで、あらゆるHRニーズに対応しています。

mixin global-office
  section.global-office
    .section-title
      h2.title グローバル拠点一覧

    .office-list
      .office-item
        figure 
          img(src="~/assets/images/aboutus/hongkong.webp")
        .content
          .title 香港
          .location 住所: Room 705-706, 7/F., China Insurance Group Building, No. 141 Des Voeux Road Central, Central, Hong Kong
      .office-item
        figure
          img(src="~/assets/images/aboutus/singapore.webp")
        .content
          .title シンガポール
          .location 住所: 3 Fraser Street, #5-25 Duo Tower, Singapore (189352)
      .office-item
        figure
          img(src="~/assets/images/aboutus/california.jpeg")
        .content
          .title アメリカ
          .location 住所：15025 PROCTOR AVAVE CITY OF INDUSTRY, Y, CA CA 91746
      .office-item
        figure
          img(src="~/assets/images/aboutus/uk.jpeg")
        .content
          .title イギリス
          .location 住所：69 Aberdeen Avenue, Cambridge, England, CB2 8DL
      .office-item
        figure
          img(src="~/assets/images/aboutus/australia.jpeg")
        .content
          .title オーストラリア
          .location 住所：135 KING STREET, SYDNEY, NSW 2000
      .office-item
        figure
          img(src="~/assets/images/aboutus/aue.jpeg")
        .content
          .title アラブ首長国連邦
          .location 住所：Office328,BlockB,Business Village, Deira, Dubai, UAE
      .office-item
        figure
          img(src="~/assets/images/aboutus/japan.jpeg")
        .content
          .title 日本
          .location 住所：神奈川県横浜市中区山下町98GSハイム山下町4階403号室
      .office-item
        figure
          img(src="~/assets/images/aboutus/korea.jpeg")
        .content
          .title 韓国
          .location 住所：서울특별시 중랑구 동일로825，2층 205호 (중화동)
      .office-item
        figure
          img(src="~/assets/images/aboutus/thailand.jpeg")
        .content
          .title タイ
          .location 住所：11 / 152-153 Room No. GDC 101, 1st Floor, Moo 5, Kookong District, Lam Luka District, Pathum Thani Province
      .office-item
        figure
          img(src="~/assets/images/aboutus/malaysia.jpeg")
        .content
          .title マレーシア
          .location 住所：332F-­2, HARMONY SQUARE, JALAN PERAK 11600 JELUTONG PULAU PINANG MALAYSIA
      .office-item
        figure
          img(src="~/assets/images/aboutus/indonesia.jpeg")
        .content
          .title インドネシア
          .location 住所：Gedung Wirausaha Lantai 1 Unit 104, Jalan HR Rasuna Said Kav. C-5,Desa/Kelurahan Karet, Kec. Setiabudi, Kota Adm. Jakarta Selatan, ProvinsiDKI Jakarta
      .office-item
        figure
          img(src="~/assets/images/aboutus/philippines.jpeg")
        .content
          .title フィリピン
          .location 住所：UNIT 25D 2ND FLOOR ZETA II BLDG.191 SALCEDO ST., SAN LORENZO,CITY OF MAKATI,FOURTH DIRECT, NATIONAL CAPITAL REGION (NCR), 1223
      .office-item
        figure
          img(src="~/assets/images/aboutus/vietnam.jpeg")
        .content
          .title ベトナム
          .location 住所：Room(s):28 ,Level 14, Saigon Centre Tower 1, No. 65 Le Loi Street, Ben Nghe Ward, District 1, Ho Chi Minh City,Vietnam
      .office-item
        figure
          img(src="~/assets/images/aboutus/beijing.webp")
        .content
          .title 北京
          .location 住所：4F, Wangfu International Center wework,Wangfujing, Dongcheng District, Beijing
      .office-item
        figure
          img(src="~/assets/images/aboutus/shanghai.webp")
        .content
          .title 上海
          .location 住所：Floor 12, Huirong Building, No. 535, Caoyang Road, Putuo District, Shanghai Chengdu: 6F-K0
      .office-item
        figure
          img(src="~/assets/images/aboutus/shenzhen.webp")
        .content
          .title 深圳
          .location 住所：0701-D021, Port Building, Ocean Freight Center, No. 59, Linhai Avenue, Nanshan Street, Shenzhen-Hong Kong Modern Service Industry Cooperation Zone in Qianhai, Shenzhen
      .office-item
        figure
          img(src="~/assets/images/aboutus/hangzhou.webp")
        .content
          .title 杭州
          .location 住所：Room 205, Building 2, No. 8-1, Longquan Road, Qianjie Street, Yuhang District, Hangzhou City, Zhejiang Province
      .office-item
        figure
          img(src="~/assets/images/aboutus/chengdu.webp")
        .content
          .title 成都
          .location 住所：6F-K0063, Lei Shing Hong Plaza, No. 5 Hangtian Road, Chenghua District, Chengdu City, Sichuan Province

mixin contact-us
  .contact-form
    //- 吃瓜就要付出代价，否则影响页面跳转
    //- https://github.com/element-plus/element-plus/pull/9731
    client-only
      el-dialog(v-model="status.showForm" title="" :width="354")
        contact-us-form(@submit="submitSuccess" lang="ja" :title="pageTitle")

.page-about-us
  .header
    +header-nav
    +company-profile

  +profession
  +global-office

  +contact-us

  SiteFooter(lang="ja" @contact-us="()=>{ status.showForm = true }")

</template>

<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {getQueryString} from "assets/utils";
definePageMeta({ layout: 'basic' })

// 获取运行时配置
const config = useRuntimeConfig()
const baseUrl = config.public.mobile_site_host || 'https://m.smartdeer.work'
const imageUrl = `${baseUrl}/images/aboutus_banner.png`
const pageUrl = `${baseUrl}/ja/aboutus`

useHead({
  htmlAttrs: { lang: 'ja' },
  title: 'SmartDeerについて - グローバル人事サービスのリーディングカンパニー | 会社概要',
  meta: [
    { name: 'description', content: 'SmartDeerは香港・シンガポールに本社を置く、世界150カ国以上で採用・雇用・給与管理サービスを提供するグローバルHRプラットフォームです。ISO 27001認証取得済みで信頼性の高いパートナーです。' },
    { name: 'keywords', content: 'SmartDeer会社概要,グローバル人事サービス,国際人材サービス,EORサービス,海外採用プラットフォーム,人事アウトソーシング,雇用ソリューション' },
    { name: 'robots', content: 'index, follow' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { name: 'author', content: 'SmartDeer' },

    // Open Graph標签
    { property: 'og:title', content: 'SmartDeerについて - グローバル人事サービスのリーディングカンパニー | 会社概要' },
    { property: 'og:description', content: 'SmartDeerは香港・シンガポールに本社を置く、世界150カ国以上で採用・雇用・給与管理サービスを提供するグローバルHRプラットフォームです。ISO 27001認証取得済みで信頼性の高いパートナーです。' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: pageUrl },
    { property: 'og:site_name', content: 'SmartDeer' },
    { property: 'og:image', content: imageUrl },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:image:type', content: 'image/png' },
    { property: 'og:image:alt', content: 'SmartDeer会社概要' },
    { property: 'og:locale', content: 'ja_JP' },

    // Twitter Card標签
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'SmartDeerについて - グローバル人事サービスのリーディングカンパニー | 会社概要' },
    { name: 'twitter:description', content: 'SmartDeerは香港・シンガポールに本社を置く、世界150カ国以上で採用・雇用・給与管理サービスを提供するグローバルHRプラットフォームです。ISO 27001認証取得済みで信頼性の高いパートナーです。' },
    { name: 'twitter:image', content: imageUrl },
    { name: 'twitter:image:alt', content: 'SmartDeer会社概要' }
  ],
  link: [
    // Canonical URL
    { rel: 'canonical', href: pageUrl },

    // Hreflang標签
    { rel: 'alternate', hreflang: 'zh', href: `${baseUrl}/zh/aboutus` },
    { rel: 'alternate', hreflang: 'en', href: `${baseUrl}/en/aboutus` },
    { rel: 'alternate', hreflang: 'ja', href: `${baseUrl}/ja/aboutus` },
    { rel: 'alternate', hreflang: 'x-default', href: `${baseUrl}/aboutus` }
  ],
  script: [
    // 结构化数据 - Organization + AboutPage
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@graph": [
          {
            "@type": "AboutPage",
            "@id": `${pageUrl}#aboutpage`,
            "url": pageUrl,
            "name": "SmartDeerについて",
            "description": "SmartDeerの会社概要ページ、グローバル人事サービスについて詳しく紹介",
            "mainEntity": {
              "@id": `${baseUrl}/#organization`
            },
            "inLanguage": "ja-JP"
          },
          {
            "@type": "Organization",
            "@id": `${baseUrl}/#organization`,
            "name": "SmartDeer",
            "url": baseUrl,
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/images/logo.png`
            },
            "description": "SmartDeerは世界をリードするグローバル人事サービスプラットフォームで、採用、雇用、給与管理などのワンストップサービスを提供",
            "foundingDate": "2020",
            "numberOfEmployees": "150+",
            "address": [
              {
                "@type": "PostalAddress",
                "addressLocality": "香港",
                "addressCountry": "HK"
              },
              {
                "@type": "PostalAddress",
                "addressLocality": "シンガポール",
                "addressCountry": "SG"
              }
            ],
            "contactPoint": {
              "@type": "ContactPoint",
              "telephone": "+852-3008-5033",
              "contactType": "customer service",
              "availableLanguage": ["Chinese", "English", "Japanese"]
            },
            "sameAs": [
              "https://www.linkedin.com/company/smartdeer-global/",
              "https://twitter.com/smartdeer"
            ],
            "hasCredential": {
              "@type": "EducationalOccupationalCredential",
              "credentialCategory": "certification",
              "name": "ISO 27001認証"
            }
          }
        ]
      })
    }
  ]
})

const status = reactive({
  showForm: false
})

function switchLang(lang) {
  langTool.swithLang(lang, '/aboutus')
}

function submitSuccess() {
  status.showForm = false
  ElMessage.success('お問い合わせありがとうございます。ご要望に応じて、担当者よりご連絡いたします。')
}

onMounted(() => {
  const autoPromptContact = getQueryString('contact')
  if (autoPromptContact === 'true') {
    setTimeout(() => {
      status.showForm = true
    }, 1000);
  }
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/en.scss';

.page-about-us {
  font-family: Helvetica;

  min-width: 375px;
  section {
    .section-title {
      text-align: center;

      h2.title {
        font-size: 20px;
        line-height: 24px;
        display: inline-block;
        position: relative;

        &::before {
          content: '';
          display: block;
          position: absolute;
          left: 0;
          bottom: 1px;
          width: 100%;
          background-color: #FF8600;
          height: 4px;
          z-index: -1;
        }
      }
    }
  }

  .header {
    background-image: url("~/assets/images/aboutus/map-bg.png");
    background-position: center 8px;
    background-repeat: no-repeat;
    background-size: auto 420px;
  }
}

section.header-nav {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 11px;

  figure.logo {
    flex: 0 0 auto;

    img {
      width: 81px;
    }
  }

  .extra {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #000000;

    .language-selector {
      margin-left: 24px;

      .text {
        font-size: 12px;
      }
    }
  }
}

section.company-profile {
  margin: 0 auto;
  max-width: 420px;
  padding: 0 20px;
  box-sizing: border-box;
  margin-top: 47px;

  .profile-content {
    padding-bottom: 47px;
    margin-top: 22px;

    figure {
      margin: 0px -16px;
      img {
        width: 100%;
      }
    }

    h3 {
      margin-top: 17px;
      font-size: 28px;
      color: #333;
    }
    h4 {
      margin-top: 20px;
      margin-bottom: 15px;
      font-size: 24px;
      color: #333;
    }
    p {
      font-size: 16px;
      letter-spacing: 1px;
      line-height: 24px;
      color: #333;
    }
  }
}

section.profession {
  background-color: #F7F9FA;
  padding: 32px 0;
  box-sizing: border-box;

  .profession-list {
    padding: 0 20px;
    box-sizing: border-box;
    max-width: 420px;
    margin: 0 auto;

    .profession-item {
      box-sizing: border-box;
      margin-bottom: 32px;

      &:last-child{
        margin-bottom: 0;
      }

      figure {
        img {
          width: 40px;
          margin: 0 auto;
        }
      }

      .content {
        margin-top: 15px;

        .title {
          font-size: 18px;
          text-align: center;
          color: #333333;
          line-height: 22px;
        }

        .desc {
          font-size: 14px;
          font-weight: 300;
          line-height: 22px;
          margin-top: 11px;
        }
      }
    }
  }
}

section.global-office {
  margin: 0 auto;
  padding: 48px 20px 40px;
  box-sizing: border-box;
  max-width: 420px;

  .office-list {
    margin-top: 22px;
    .office-item {
      box-sizing: border-box;
      margin-bottom: 40px;
      &:last-child{
        margin-bottom: 0;
      }

      figure {
        margin-bottom: 24px;

        img {
          width: 100%;
        }
      }

      .content {
        margin-top: 21px;

        .title {
          font-size: 18px;
          color: #333333;
          margin-bottom: 10px;
          text-align: center;
          line-height: 22px;
        }

        .location, .contact{
          font-size: 14px;
          color: #333333;
          line-height: 21px;
        }
      }
    }
  }
}
</style>