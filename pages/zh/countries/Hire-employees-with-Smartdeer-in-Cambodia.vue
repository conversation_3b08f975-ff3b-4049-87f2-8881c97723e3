<template lang="pug">
.contries-page
  site-header(lang="zh" @contact-us="status.showForm = true")

  .countries
    .countries-banner
      .banner
        figure
          img(v-if="bannerImage" :src="bannerImage")
      .flag
        figure
          img(v-if="flagImage" :src="flagImage")
    .countries-content
      h1.article-title 在柬埔寨雇佣
      .form-box
        client-only
          contact-us-form(@submit="submitSuccess" lang="zh" textareaResize="none")
      div(v-html="htmlContent" )
  site-footer(lang="zh" @contact-us="status.showForm = true")
</template>
<script lang="ts" setup>
import langTool from '~/assets/utils/lang'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElMessage, ElDialog } from 'element-plus'
import {useSeoMeta} from "@unhead/vue";
definePageMeta({
  layout: 'basic'
})
useSeoMeta({
  ogTitle: '在柬埔寨雇佣',
  ogDescription: '基础信息   首都：金边 Phnom Penh   时区：GMT+7 […]',
  ogSiteName: 'SmartDeer',
  description: '基础信息   首都：金边 Phnom Penh   时区：GMT+7 […]'
})
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  title: '在柬埔寨雇佣'
})
const status = reactive({
  showForm: false
})
const langSimple = 'zh';
const pageTitle = '在柬埔寨雇佣';
const bannerImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/柬埔寨FI图正版.png';
const flagImage = 'https://blog.smartdeer.work/wp-content/uploads/2024/05/柬埔寨国旗正版.jpg';
const htmlContent = '<h2 class="wp-block-heading has-large-font-size">基础信息</h2><p>&nbsp;&nbsp;首都：金边 Phnom Penh</p><p>&nbsp;&nbsp;时区：GMT+7</p><p>&nbsp;&nbsp;语言：高棉语</p><p>&nbsp;&nbsp;货币代码：KHR</p><h2 class="wp-block-heading has-large-font-size">人力资源制度概况</h2><h3 class="wp-block-heading has-large-font-size">雇佣合同</h3><h3 class="wp-block-heading has-medium-font-size">  -提供的职位</h3><p>&nbsp;&nbsp;&#8211; 合同期限</p><p>&nbsp;&nbsp;&#8211; 工作地点</p><p>&nbsp;&nbsp;&#8211; 基本工资和支付日期</p><p>&nbsp;&nbsp;&#8211; 工作时间和工作日</p><p>&nbsp;&nbsp;&#8211; 其他福利（如果有）</p><p>&nbsp;&nbsp;&#8211; 加班费（如果有）</p><p>&nbsp;&nbsp;&#8211; 年假</p><p>&nbsp;&nbsp;&#8211; 终止条款</p><p>&nbsp;&nbsp;&#8211; 病假和住院假期</p><p>&nbsp;&nbsp;&#8211; 休息日</p><p>&nbsp;&nbsp;&#8211; 试用期</p><p>&nbsp;&nbsp;&#8211; 通知期</p><p>&nbsp;&nbsp;&#8211; 职责和责任</p><p>&nbsp;&nbsp;&#8211; 终止条款</p><p>&nbsp;&nbsp;&#8211; 法定扣除和所得税</p><h2 class="wp-block-heading has-large-font-size">试用期相关规定</h2><p>试用期，正式员工不得超过三个月，专业工不得超过两个月，非专业工不得超过一个月。</p><h2 class="wp-block-heading has-large-font-size">工作时间相关规定</h2><p>标准工作时间为每天8小时，每周40小时。标准工作周为周一至周五。然而，员工可以选择周一至周六的工作周，每周工作48小时。 加班费是强制性的。超出标准工作时间的小时数被视为加班。员工每个工作日最多可以加班2小时。对于额外的小时数，员工的加班费率为：</p><p>-加班在晚上 10 点之前完成，则为员工工资的 150% </p><p>-加班在晚上 10 点之后、周日或公共假期，则为员工工资的 200%</p><h2 class="wp-block-heading has-large-font-size">最低工资标准</h2><ol start="1"><li></li></ol><p>2024年的最低工资为每月KHR 1,069,900，具体数额可能根据行业有所不同。</p><h2 class="wp-block-heading has-large-font-size">雇主成本</h2><p>雇主成本通常估计为员工工资的5.40%</p><p>养老基金 &#8211; 2%<br>职业风险贡献 &#8211; 0.80%<br>医疗保健贡献 &#8211; 2.60%</p><h2 class="wp-block-heading has-large-font-size">终止规则</h2><h3 class="wp-block-heading">终止原因</h3><p>解雇必须有正当理由，即未能按照雇主向员工明确的合理标准执行。</p><p>在柬埔寨，试用期雇员可能因正当理由被解雇。如果员工未能按照合理标准成为正式员工，也可能会被解雇。雇主必须在雇员受聘时让其了解这些标准。用人单位应提前7天通知并支付剩余工资。</p><h2 class="wp-block-heading has-large-font-size">通知期</h2><p>通知期限根据连续服务年限确定如下：雇佣期为</p><p>6个月以内 &#8211; 1周的通知期<br>6个月至2年的雇佣期 &#8211; 2周的通知期<br>2至5年的雇佣期 &#8211; 1个月的通知期<br>5至10年的雇佣期 &#8211; 2个月的通知期<br>超过10年的雇佣期 &#8211; 3个月的通知期</p><h2 class="wp-block-heading has-large-font-size">离职补偿</h2><p>如果员工因非严重不当行为被解雇，雇主必须支付解雇赔偿金。赔偿金的金额取决于员工连续服务的长度，最多可达6个月。</p><h2 class="wp-block-heading has-large-font-size">薪酬与假期</h2><h3 class="wp-block-heading has-large-font-size">薪资支付</h3><p>体力劳动者每月必须至少支付两次工资，发薪日间隔不得超过 16 天。</p><p>正式员工必须每月至少支付一次工资。</p><p>任何以佣金为基础工作的员工必须至少每三个月支付一次工资。</p><h2 class="wp-block-heading has-large-font-size">工资单</h2><p>工资单可以以数字方式或纸质形式提供。</p><h2 class="wp-block-heading has-large-font-size">年假</h2><p>雇主必须为员工提供每月 1.5 天的带薪年假。此要求每年总共不得超过 18 天。员工每连续工作三年，每年有权额外享受一天的休假。例如，员工连续工作第三年后，带薪年假天数将增加至每年19天。六年后，将增加到每年20天。</p><h2 class="wp-block-heading has-large-font-size">病假</h2><p>员工有权获得最多12天的带薪病假。如果员工连续请假超过3天，需要提供授权医生的医疗证明。</p><h2 class="wp-block-heading has-large-font-size">产假&amp;育儿假</h2><p>孕妇连续工作一年后有权享受90天的产假。对于休假是否必须在出生之前或之后开始没有限制。</p><p>雇员在产假期间有权享受正常工资和福利的50%（雇主支付）。如果雇员未完成工作一年，她将有权享受无薪产假。</p><h2 class="wp-block-heading has-large-font-size">税务和社会保险</h2><h3 class="wp-block-heading">个人所得税</h3><ol start="2"><li></li></ol><p>柬埔寨虽然不征收个人所得税，但员工需要根据其工资缴纳贡献金，并缴纳工资税。工资税的范围从0%到20%，并有最大贡献额限制。</p><figure class="wp-block-table"><table><tbody><tr><td>年度总收入</td><td>税率 （%）</td><td>最高缴纳数</td></tr><tr><td>From 0 to 1.500.000 KHR</td><td>0%</td><td>0</td></tr><tr><td>From 1.500.000 KHR to 2.000.000 KHR</td><td>5%</td><td>75.000 KHR</td></tr><tr><td>From 2.000.000 KHR to 8.500.000 KHR</td><td>10%</td><td>175.000 KHR</td></tr><tr><td>From 8.500.000 KHR to 12.500.000 KHR</td><td>15%</td><td>600.000 KHR</td></tr><tr><td>Over 12.500.000 KHR</td><td>20%</td><td>1.225.000 KHR</td></tr></tbody></table></figure><h3 class="wp-block-heading">社会保险</h3><p>福利分为：</p><p>-医疗福利</p><p>-暂时伤残福利 </p><p>-护理福利</p><p>-永久伤残福利</p><p>-持续护理福利 </p><p>-幸存者福利</p><figure class="wp-block-table"><table><tbody><tr><td></td><td>&nbsp;Employer&nbsp;(%)雇主（%）</td><td>&nbsp;Employee&nbsp;(%)雇员（%）</td></tr><tr><td>Occupational Risks Contribution</td><td>&nbsp;0.8</td><td>&nbsp;0.0</td></tr><tr><td>Health Care Contribution</td><td>&nbsp;2.6</td><td>&nbsp;0.0</td></tr><tr><td>Pension Contribution</td><td>&nbsp;2.0</td><td>&nbsp;2.0</td></tr><tr><td>Total</td><td>&nbsp;6.8</td><td>&nbsp;2.0</td></tr><tr><td>Maximum Monthly Contribution(estimated)</td><td>&nbsp;KHR64,800/USD 16</td><td>&nbsp;KHR24,000/USD6</td></tr></tbody></table></figure><h2 class="wp-block-heading has-large-font-size">2024年法定假期&nbsp;&nbsp;</h2><figure class="wp-block-table"><table><tbody><tr><td>&nbsp;假期</td><td>&nbsp;&nbsp;&nbsp;日期</td></tr><tr><td>&nbsp;International New Year&#8217;s Day</td><td>&nbsp;&nbsp; 1.1</td></tr><tr><td>&nbsp;Victory over Genocide Day</td><td>&nbsp;&nbsp; 1.7</td></tr><tr><td>&nbsp;International Women&#8217;s Day</td><td>&nbsp;&nbsp; 3.8</td></tr><tr><td>&nbsp;Khmer New Year&#8217;s Day</td><td>&nbsp;&nbsp; 4.13-4.16</td></tr><tr><td>&nbsp;International Labour Day</td><td>&nbsp;&nbsp; 5.1</td></tr><tr><td>&nbsp;King Norodom Sihamoni&#8217;s Birthday</td><td>&nbsp;&nbsp; 5.14</td></tr><tr><td>&nbsp;Visgk Bochea Day</td><td>&nbsp;&nbsp; 5.22</td></tr><tr><td>&nbsp;Royal Plowing Ceremony Day</td><td>&nbsp;&nbsp; 5.26</td></tr><tr><td>&nbsp;Queen Monineath&#8217;s Birthday</td><td>&nbsp;&nbsp; 7.18</td></tr><tr><td>&nbsp;Constitution Day</td><td>&nbsp;&nbsp; 9.24</td></tr><tr><td>&nbsp;Pchum Ben Festival</td><td>&nbsp;&nbsp; 10.1-10.3</td></tr><tr><td>&nbsp;Commemoration Day of King&#8217;s Father</td><td>&nbsp;&nbsp; 10.15</td></tr><tr><td>&nbsp;King Norodom Sihamoni&#8217;s Coronation Day</td><td>&nbsp;&nbsp; 10.29</td></tr><tr><td>&nbsp;Independence Day</td><td>&nbsp;&nbsp; 11.9</td></tr><tr><td>&nbsp;Water Festival</td><td>&nbsp;&nbsp; 11.14 -11.16</td></tr></tbody></table></figure><h1 class="wp-block-heading"></h1>';
function submitSuccess() {
  ElMessage.success('您的请求已收到，我们会尽快与您联系。')
}
function handleScrollToTop() {
  document.documentElement.scrollTop = 0;
}
</script>
<style lang="scss">
@import './article-detail.scss';
</style>