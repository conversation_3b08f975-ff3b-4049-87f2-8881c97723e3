# Telegram 链接预览缩略图修复总结

## 问题描述
在Telegram聊天中分享 `pages/zh/index.vue` 页面的URL时，链接预览不显示缩略图。虽然已经配置了Open Graph图片meta标签 `{ property: 'og:image', content: '/images/tg_banner.png' }`，但Telegram无法正确抓取和显示预览图片。

## 问题分析

### 根本原因
1. **相对路径问题**：原始配置使用相对路径 `/images/tg_banner.png`，而Telegram需要完整的绝对URL才能正确抓取图片
2. **缺少图片元信息**：缺少图片尺寸、类型等meta标签，影响社交媒体平台的图片处理
3. **URL不匹配**：og:url配置与实际访问的移动端域名不一致

### 技术细节
- **图片文件**：`public/images/tg_banner.png` (1390x781像素，PNG格式)
- **域名配置**：生产环境使用 `https://m.smartdeer.work`
- **影响范围**：中文、英文、日文三个语言版本的首页

## 解决方案

### 1. 修复中文版本 (`pages/zh/index.vue`)
```javascript
// 获取运行时配置
const config = useRuntimeConfig()
const baseUrl = config.public.mobile_site_host || 'https://m.smartdeer.work'
const imageUrl = `${baseUrl}/images/tg_banner.png`
const pageUrl = `${baseUrl}/zh`

// 更新meta标签配置
useHead({
  meta: [
    // ... 其他标签
    { property: 'og:url', content: pageUrl },
    { property: 'og:image', content: imageUrl },
    { property: 'og:image:width', content: '1390' },
    { property: 'og:image:height', content: '781' },
    { property: 'og:image:type', content: 'image/png' },
    { property: 'og:image:alt', content: 'SmartDeer - 全球人力资源一站式服务' },
    // Twitter标签也使用绝对URL
    { name: 'twitter:image', content: imageUrl },
    { name: 'twitter:image:alt', content: 'SmartDeer - 全球人力资源一站式服务' }
  ]
})
```

### 2. 修复英文版本 (`pages/en/index.vue`)
- 应用相同的绝对URL配置
- 更新页面URL为 `${baseUrl}/en`
- 调整alt文本为英文版本

### 3. 修复日文版本 (`pages/ja/index.vue`)
- 应用相同的绝对URL配置
- 更新页面URL为 `${baseUrl}/ja`
- 调整alt文本为日文版本

## 关键改进

### Open Graph 标签完善
- ✅ `og:image` - 使用绝对URL
- ✅ `og:image:width` - 图片宽度 (1390px)
- ✅ `og:image:height` - 图片高度 (781px)
- ✅ `og:image:type` - 图片类型 (image/png)
- ✅ `og:image:alt` - 图片描述
- ✅ `og:url` - 使用正确的移动端域名

### Twitter Card 标签完善
- ✅ `twitter:image` - 使用绝对URL
- ✅ `twitter:image:alt` - 图片描述

### 动态配置支持
- 使用 `useRuntimeConfig()` 获取环境配置
- 支持开发、测试、生产环境的不同域名
- 自动构建正确的绝对URL

## 验证方法

### 1. 开发环境测试
```bash
npm run dev
# 访问 http://localhost:3000/zh
```

### 2. Meta标签检查工具
创建了 `test-og-tags.html` 工具用于验证：
- 检查所有Open Graph和Twitter meta标签
- 验证图片URL是否为绝对路径
- 预览图片是否可正常加载

### 3. 社交媒体验证
- [Facebook Sharing Debugger](https://developers.facebook.com/tools/debug/)
- [Twitter Card Validator](https://cards-dev.twitter.com/validator)
- [LinkedIn Post Inspector](https://www.linkedin.com/post-inspector/)

## 预期效果

修复后，在Telegram中分享页面链接时应该能够：
1. ✅ 正确显示页面标题
2. ✅ 显示页面描述
3. ✅ 显示缩略图 (1390x781 PNG图片)
4. ✅ 链接到正确的页面URL

## 注意事项

1. **缓存清理**：社交媒体平台可能会缓存Open Graph数据，修复后可能需要等待或手动刷新缓存
2. **图片可访问性**：确保图片文件在生产环境中可以通过HTTP正常访问
3. **域名配置**：确认 `nuxt.config.ts` 中的域名配置正确
4. **多语言支持**：三个语言版本都已修复，确保一致性

## 文件修改清单

- ✅ `pages/zh/index.vue` - 中文版本修复
- ✅ `pages/en/index.vue` - 英文版本修复  
- ✅ `pages/ja/index.vue` - 日文版本修复
- ✅ `test-og-tags.html` - 测试工具创建

修复完成后，Telegram链接预览应该能够正常显示缩略图。
